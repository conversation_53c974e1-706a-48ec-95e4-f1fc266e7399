#!/bin/bash

# VPS压力测试 - 1万用户写入测试脚本
# 使用方法: ./scripts/run_10k_users_test.sh

set -e

echo "🚀 VPS服务器极限压力测试 - 1万用户写入测试"
echo "=================================================="

# 检查Dart环境
if ! command -v dart &> /dev/null; then
    echo "❌ 错误: 未找到Dart环境，请先安装Dart SDK"
    exit 1
fi

# 检查配置文件
CONFIG_FILE="config/10k_users_write_test.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

echo "📋 测试配置:"
echo "   - 配置文件: $CONFIG_FILE"
echo "   - 测试类型: 写入压力测试"
echo "   - 目标用户数: 10,000"
echo "   - 预计测试时间: 2小时"
echo ""

# 询问用户确认
read -p "⚠️  这是一个高强度压力测试，确认要继续吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 测试已取消"
    exit 1
fi

# 创建报告目录
mkdir -p reports
mkdir -p logs

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/10k_users_test_$TIMESTAMP.log"

echo "📊 开始测试..."
echo "   - 日志文件: $LOG_FILE"
echo "   - 报告目录: reports/"
echo ""

# 运行测试并记录日志
{
    echo "=== VPS压力测试开始 ==="
    echo "开始时间: $(date)"
    echo "配置文件: $CONFIG_FILE"
    echo "========================="
    echo ""
    
    dart run bin/vps_stress_test.dart \
        -c "$CONFIG_FILE" \
        -t write \
        --generate-markdown \
        --generate-json \
        --generate-csv
        
    echo ""
    echo "========================="
    echo "测试完成时间: $(date)"
    echo "=== VPS压力测试结束 ==="
    
} 2>&1 | tee "$LOG_FILE"

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 测试成功完成！"
    echo ""
    echo "📊 查看测试结果:"
    echo "   - 日志文件: $LOG_FILE"
    echo "   - 报告目录: reports/"
    echo ""
    
    # 列出生成的报告文件
    if [ -d "reports" ] && [ "$(ls -A reports)" ]; then
        echo "📋 生成的报告文件:"
        ls -la reports/ | grep "$(date +%Y%m%d)" || ls -la reports/ | tail -5
    fi
    
    echo ""
    echo "🎯 关键指标查看:"
    echo "   - 最大并发用户数"
    echo "   - 平均响应时间"
    echo "   - 成功率"
    echo "   - 系统资源使用情况"
    
else
    echo ""
    echo "❌ 测试执行失败，请检查日志文件: $LOG_FILE"
    echo ""
    echo "🔍 常见问题排查:"
    echo "   1. 检查服务器是否可访问"
    echo "   2. 检查网络连接是否稳定"
    echo "   3. 检查服务器资源是否充足"
    echo "   4. 检查配置文件是否正确"
    
    exit 1
fi

echo ""
echo "📖 更多信息请参考: docs/VPS_STRESS_TEST_GUIDE.md"
echo "🎉 测试完成！"
