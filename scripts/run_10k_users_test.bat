@echo off
REM VPS压力测试 - 1万用户写入测试脚本 (Windows版本)
REM 使用方法: scripts\run_10k_users_test.bat

setlocal enabledelayedexpansion

echo 🚀 VPS服务器极限压力测试 - 1万用户写入测试
echo ==================================================

REM 检查Dart环境
dart --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Dart环境，请先安装Dart SDK
    pause
    exit /b 1
)

REM 检查配置文件
set CONFIG_FILE=config\10k_users_write_test.json
if not exist "%CONFIG_FILE%" (
    echo ❌ 错误: 配置文件不存在: %CONFIG_FILE%
    pause
    exit /b 1
)

echo 📋 测试配置:
echo    - 配置文件: %CONFIG_FILE%
echo    - 测试类型: 写入压力测试
echo    - 目标用户数: 10,000
echo    - 预计测试时间: 2小时
echo.

REM 询问用户确认
set /p CONFIRM="⚠️  这是一个高强度压力测试，确认要继续吗？(y/N): "
if /i not "%CONFIRM%"=="y" (
    echo ❌ 测试已取消
    pause
    exit /b 1
)

REM 创建报告目录
if not exist "reports" mkdir reports
if not exist "logs" mkdir logs

REM 获取当前时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "TIMESTAMP=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"
set "LOG_FILE=logs\10k_users_test_%TIMESTAMP%.log"

echo 📊 开始测试...
echo    - 日志文件: %LOG_FILE%
echo    - 报告目录: reports\
echo.

REM 运行测试并记录日志
(
    echo === VPS压力测试开始 ===
    echo 开始时间: %date% %time%
    echo 配置文件: %CONFIG_FILE%
    echo =========================
    echo.
    
    dart run bin/vps_stress_test.dart ^
        -c "%CONFIG_FILE%" ^
        -t write ^
        --generate-markdown ^
        --generate-json ^
        --generate-csv
        
    echo.
    echo =========================
    echo 测试完成时间: %date% %time%
    echo === VPS压力测试结束 ===
) 2>&1 | tee "%LOG_FILE%"

REM 检查测试结果
if errorlevel 1 (
    echo.
    echo ❌ 测试执行失败，请检查日志文件: %LOG_FILE%
    echo.
    echo 🔍 常见问题排查:
    echo    1. 检查服务器是否可访问
    echo    2. 检查网络连接是否稳定
    echo    3. 检查服务器资源是否充足
    echo    4. 检查配置文件是否正确
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 测试成功完成！
    echo.
    echo 📊 查看测试结果:
    echo    - 日志文件: %LOG_FILE%
    echo    - 报告目录: reports\
    echo.
    
    REM 列出生成的报告文件
    if exist "reports\*.*" (
        echo 📋 生成的报告文件:
        dir /b /o-d reports\ | findstr /r ".*" | head -5
    )
    
    echo.
    echo 🎯 关键指标查看:
    echo    - 最大并发用户数
    echo    - 平均响应时间
    echo    - 成功率
    echo    - 系统资源使用情况
)

echo.
echo 📖 更多信息请参考: docs\VPS_STRESS_TEST_GUIDE.md
echo 🎉 测试完成！
pause
