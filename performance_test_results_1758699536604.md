# 后端性能测试对比报告

**测试时间**: 2025-09-24T15:38:56.604882

## 测试配置

### 查询测试配置
- 查询迭代次数: 2000
- 查询批次大小: 100

## 查询性能测试结果

### 查询性能对比

| 查询类型 | PocketBase (queries/s) | TrailBase (queries/s) | 性能对比 |
|---------|----------------------|---------------------|----------|
| ID查询 | 468.93 | 1138.30 | TrailBase +142.7% |
| 条件查询 | 9.12 | 359.84 | TrailBase +3845.0% |
| 范围查询 | 7.50 | 360.36 | TrailBase +4707.1% |
| 分页查询 | 14.87 | 361.21 | TrailBase +2329.8% |
| 聚合查询 | 15.08 | 230.28 | TrailBase +1426.9% |
| 5线程并发 | 16.40 | 2684.56 | TrailBase +16269.4% |
| 10线程并发 | 13.84 | 2766.25 | TrailBase +19892.3% |
| 20线程并发 | 11.67 | 2702.70 | TrailBase +23050.5% |

### 详细查询测试结果

#### PocketBase

| 查询类型 | 总耗时(ms) | 成功查询 | 总查询数 | 查询速度(queries/s) | 成功率(%) |
|---------|-----------|---------|---------|-------------------|----------|
| id_query | 4265 | 2000 | 2000 | 468.93 | 100.0 |
| condition_query | 109631 | 1000 | 1000 | 9.12 | 100.0 |
| range_query | 133396 | 1000 | 1000 | 7.50 | 100.0 |
| pagination_query | 134536 | 2000 | 2000 | 14.87 | 100.0 |
| aggregation_query | 26522 | 400 | 400 | 15.08 | 100.0 |
| concurrent_query_5threads | 121952 | 2000 | 2000 | 16.40 | 100.0 |
| concurrent_query_10threads | 144544 | 2000 | 2000 | 13.84 | 100.0 |
| concurrent_query_20threads | 171314 | 2000 | 2000 | 11.67 | 100.0 |

#### TrailBase

| 查询类型 | 总耗时(ms) | 成功查询 | 总查询数 | 查询速度(queries/s) | 成功率(%) |
|---------|-----------|---------|---------|-------------------|----------|
| id_query | 1757 | 2000 | 2000 | 1138.30 | 100.0 |
| condition_query | 2779 | 1000 | 1000 | 359.84 | 100.0 |
| range_query | 2775 | 1000 | 1000 | 360.36 | 100.0 |
| pagination_query | 5537 | 2000 | 2000 | 361.21 | 100.0 |
| aggregation_query | 1737 | 400 | 400 | 230.28 | 100.0 |
| concurrent_query_5threads | 745 | 2000 | 2000 | 2684.56 | 100.0 |
| concurrent_query_10threads | 723 | 2000 | 2000 | 2766.25 | 100.0 |
| concurrent_query_20threads | 740 | 2000 | 2000 | 2702.70 | 100.0 |

## 综合性能总结

### 查询性能
- **最佳查询性能**: TrailBase_concurrent_query_10threads
  - 速度: 2766.25 queries/s
  - 成功率: 100.0%

## 推荐建议

### 选择TrailBase的场景
- 需要高性能数据插入和查询的应用
- 对并发写入有较高要求的系统
- 表结构相对固定的项目

### 选择PocketBase的场景
- 需要快速原型开发和部署
- 表结构经常变化的项目
- 需要完整后端功能和管理界面的应用

