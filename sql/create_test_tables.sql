-- VPS压力测试数据库表创建脚本
-- 适用于TrailBase (SQLite/PostgreSQL) 和 PocketBase

-- =====================================================
-- TrailBase 测试表 (SQLite语法)
-- =====================================================

-- 创建VPS压力测试表
CREATE TABLE IF NOT EXISTS vps_stress_test (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    age INTEGER NOT NULL,
    score REAL NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    description TEXT NOT NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_name ON vps_stress_test(name);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_email ON vps_stress_test(email);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_age ON vps_stress_test(age);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_score ON vps_stress_test(score);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_created_at ON vps_stress_test(created_at);

-- =====================================================
-- PostgreSQL 版本 (如果TrailBase使用PostgreSQL)
-- =====================================================

-- 创建VPS压力测试表 (PostgreSQL语法)
/*
CREATE TABLE IF NOT EXISTS vps_stress_test (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    age INTEGER NOT NULL,
    score DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT NOT NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_name ON vps_stress_test(name);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_email ON vps_stress_test(email);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_age ON vps_stress_test(age);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_score ON vps_stress_test(score);
CREATE INDEX IF NOT EXISTS idx_vps_stress_test_created_at ON vps_stress_test(created_at);
*/

-- =====================================================
-- 测试数据插入示例
-- =====================================================

-- 插入一些测试数据
INSERT INTO vps_stress_test (name, email, age, score, description) VALUES
('张三', '<EMAIL>', 25, 85.5, '这是一个测试用户记录'),
('李四', '<EMAIL>', 30, 92.0, '另一个测试用户记录'),
('王五', '<EMAIL>', 28, 78.3, '第三个测试用户记录');

-- =====================================================
-- 查询测试示例
-- =====================================================

-- 基本查询
SELECT * FROM vps_stress_test LIMIT 10;

-- 按年龄查询
SELECT * FROM vps_stress_test WHERE age > 25;

-- 按分数范围查询
SELECT * FROM vps_stress_test WHERE score BETWEEN 80.0 AND 95.0;

-- 按名称模糊查询
SELECT * FROM vps_stress_test WHERE name LIKE '%张%';

-- 分页查询
SELECT * FROM vps_stress_test ORDER BY created_at DESC LIMIT 10 OFFSET 0;

-- 统计查询
SELECT COUNT(*) as total_records FROM vps_stress_test;
SELECT AVG(score) as average_score FROM vps_stress_test;
SELECT MAX(age) as max_age, MIN(age) as min_age FROM vps_stress_test;

-- =====================================================
-- 性能测试相关查询
-- =====================================================

-- 复杂查询测试
SELECT 
    name,
    email,
    age,
    score,
    CASE 
        WHEN score >= 90 THEN '优秀'
        WHEN score >= 80 THEN '良好'
        WHEN score >= 70 THEN '中等'
        ELSE '需要改进'
    END as grade
FROM vps_stress_test 
WHERE age BETWEEN 20 AND 40
ORDER BY score DESC;

-- 聚合查询测试
SELECT 
    CASE 
        WHEN age < 25 THEN '年轻组'
        WHEN age < 35 THEN '中年组'
        ELSE '成熟组'
    END as age_group,
    COUNT(*) as count,
    AVG(score) as avg_score,
    MAX(score) as max_score,
    MIN(score) as min_score
FROM vps_stress_test 
GROUP BY 
    CASE 
        WHEN age < 25 THEN '年轻组'
        WHEN age < 35 THEN '中年组'
        ELSE '成熟组'
    END
ORDER BY avg_score DESC;

-- =====================================================
-- 清理和维护
-- =====================================================

-- 清空测试数据
-- DELETE FROM vps_stress_test;

-- 重置自增ID (SQLite)
-- DELETE FROM sqlite_sequence WHERE name='vps_stress_test';

-- 删除表 (谨慎使用)
-- DROP TABLE IF EXISTS vps_stress_test;

-- =====================================================
-- 使用说明
-- =====================================================

/*
使用方法:

1. 对于TrailBase:
   - 连接到TrailBase数据库
   - 执行上面的SQLite版本SQL语句
   - 如果使用PostgreSQL后端，使用注释中的PostgreSQL版本

2. 对于PocketBase:
   - PocketBase使用自己的集合(Collection)系统
   - 不需要手动创建SQL表
   - PocketBase会自动处理数据结构

3. 测试验证:
   - 执行插入示例数据
   - 运行查询测试确保表结构正确
   - 检查索引是否生效

4. 性能优化:
   - 根据实际查询模式调整索引
   - 监控查询性能
   - 必要时添加更多索引

注意事项:
- 在生产环境中运行前请先在测试环境验证
- 大量数据插入前请确保有足够的存储空间
- 定期清理测试数据以避免数据库过大
*/
