import 'dart:io';
import 'package:args/args.dart';
import '../lib/services/pocketbase_service.dart';
import '../lib/services/trailbase_service_simplified.dart';
import '../lib/tests/performance_test.dart';
import '../lib/tests/query_performance_test.dart';
import '../lib/tests/realtime_connection_test.dart';
import '../lib/config/test_config.dart';
import '../lib/config/server_config.dart';
import '../lib/config/connection_test_config.dart';
import '../lib/models/connection_test_result.dart';
import '../lib/utils/unified_report_generator.dart';
import '../lib/models/test_record.dart';

Future<void> main(List<String> arguments) async {
  final parser = ArgParser()
    // 阶段控制
    ..addFlag('enable-insert', defaultsTo: true, help: '启用插入测试')
    ..addFlag('enable-query', defaultsTo: false, help: '启用查询测试')
    ..addFlag('enable-connection', defaultsTo: false, help: '启用连接测试')
    // 插入测试配置
    ..addOption('records', abbr: 'r', defaultsTo: '100000', help: '要插入的记录数量')
    ..addOption('batch-size', abbr: 'b', defaultsTo: '2000', help: '批量插入的批次大小')
    ..addOption(
      'concurrency',
      abbr: 'c',
      defaultsTo: '1,2,4,8',
      help: '并发级别（逗号分隔）',
    )
    ..addOption('interval', abbr: 'i', defaultsTo: '10', help: '后端测试间隔时间（秒）')
    // 插入测试类型控制
    ..addFlag('enable-single-insert', defaultsTo: true, help: '启用单条插入测试')
    ..addFlag('enable-batch-insert', defaultsTo: true, help: '启用批量插入测试')
    ..addFlag('enable-concurrent-insert', defaultsTo: true, help: '启用并发插入测试')
    // 查询测试配置
    ..addOption('query-iterations', defaultsTo: '2000', help: '查询迭代次数')
    ..addOption('query-batch-size', defaultsTo: '100', help: '查询批次大小')
    ..addFlag('enable-id-query', defaultsTo: true, help: '启用ID查询测试')
    ..addFlag('enable-condition-query', defaultsTo: true, help: '启用条件查询测试')
    ..addFlag('enable-range-query', defaultsTo: true, help: '启用范围查询测试')
    ..addFlag('enable-pagination-query', defaultsTo: true, help: '启用分页查询测试')
    ..addFlag('enable-aggregation-query', defaultsTo: true, help: '启用聚合查询测试')
    ..addOption('query-concurrency', defaultsTo: '5,10,20', help: '查询并发级别，逗号分隔')
    // 连接测试配置
    ..addOption(
      'connection-levels',
      help: '连接数级别，逗号分隔 (默认: 5000,10000,15000,20000,30000,50000)',
    )
    ..addOption('test-duration', defaultsTo: '10', help: '每个级别测试时间（分钟）')
    ..addOption('connection-interval', defaultsTo: '5', help: '连接建立间隔（毫秒）')
    ..addOption('concurrent-connections', defaultsTo: '100', help: '并发连接数')
    ..addOption('max-memory', defaultsTo: '4096', help: '最大内存使用限制（MB）')
    ..addOption('max-cpu', defaultsTo: '90', help: '最大CPU使用率限制（%）')
    ..addOption('message-push-interval', defaultsTo: '5', help: '消息推送间隔（秒）')
    ..addFlag('enable-pure-connection', defaultsTo: true, help: '启用纯长连接测试')
    ..addFlag('enable-heartbeat', defaultsTo: false, help: '启用心跳测试')
    ..addFlag('enable-message-push', defaultsTo: true, help: '启用消息推送测试')
    ..addFlag('enable-connection-speed', defaultsTo: false, help: '启用连接速度测试')
    // 服务器配置
    ..addOption('environment', abbr: 'e', help: '服务器环境 (local/vps)，默认从配置文件读取')
    ..addOption(
      'config-file',
      defaultsTo: 'config/servers.json',
      help: '服务器配置文件路径',
    )
    ..addOption(
      'test-config',
      defaultsTo: 'config/test_config.json',
      help: '测试配置文件路径',
    )
    ..addOption('pocketbase-url', help: 'PocketBase服务器URL（覆盖配置文件）')
    ..addOption('trailbase-url', help: 'TrailBase服务器URL（覆盖配置文件）')
    ..addFlag('pocketbase-only', defaultsTo: false, help: '只测试PocketBase')
    ..addFlag('trailbase-only', defaultsTo: false, help: '只测试TrailBase')
    // 报告配置
    ..addFlag('generate-json', defaultsTo: false, help: '生成JSON报告')
    ..addFlag('generate-csv', defaultsTo: false, help: '生成CSV报告')
    ..addFlag('generate-md', defaultsTo: true, help: '生成Markdown报告')
    ..addFlag('help', abbr: 'h', help: '显示帮助信息');

  try {
    final results = parser.parse(arguments);

    if (results['help'] as bool) {
      print('后端性能测试工具');
      print('');
      print('用法: dart run bin/main.dart [选项]');
      print('');
      print('示例:');
      print('  # 使用VPS服务器运行插入测试');
      print('  dart run bin/main.dart --records=10000 --batch-size=1000');
      print('');
      print('  # 使用本地服务器运行查询测试');
      print(
        '  dart run bin/main.dart --environment=local --enable-insert=false --enable-query=true',
      );
      print('');
      print('  # 只运行单条插入测试');
      print(
        '  dart run bin/main.dart --enable-batch-insert=false --enable-concurrent-insert=false',
      );
      print('');
      print('  # 使用自定义测试配置文件');
      print('  dart run bin/main.dart --test-config=my_test_config.json');
      print('');
      print('  # 自定义服务器地址');
      print(
        '  dart run bin/main.dart --pocketbase-url=http://192.168.1.100:8090 --trailbase-url=http://192.168.1.100:4001',
      );
      print('');
      print('  # 运行完整测试（插入+查询）');
      print('  dart run bin/main.dart --enable-query=true --records=50000');
      print('');
      print('注意: 如需运行VPS极限压力测试，请使用:');
      print('  dart run bin/vps_stress_test.dart');
      print('');
      print(parser.usage);
      return;
    }

    // 加载服务器配置
    final serverConfig = await ServerConfig.loadFromFile(
      configPath: results['config-file'] as String,
      environment: results['environment'] as String?,
      pocketbaseUrl: results['pocketbase-url'] as String?,
      trailbaseUrl: results['trailbase-url'] as String?,
    );

    // 打印服务器配置
    serverConfig.printConfig();

    // 首先从JSON配置文件加载配置，然后用命令行参数覆盖
    final testConfigPath = results['test-config'] as String;
    TestConfig config;

    try {
      // 先从JSON文件加载配置
      config = TestConfig.fromJsonFile(testConfigPath);
      print('✓ 已从配置文件加载测试配置: $testConfigPath');

      // 然后用命令行参数覆盖（如果提供了的话）
      config = TestConfig.fromArgs(results);
      print('✓ 命令行参数已应用到配置');
    } catch (e) {
      print('⚠️ 配置文件加载失败，使用命令行参数: $e');
      config = TestConfig.fromArgs(results);
    }

    // 验证配置
    if (!config.validate()) {
      return;
    }

    // 打印测试配置信息
    config.printConfig();

    // 运行测试
    await runPerformanceTests(config, results, serverConfig);
  } catch (e) {
    print('错误: $e');
    print('');
    print('使用 --help 查看帮助信息');
  }
}

/// 运行性能测试
Future<void> runPerformanceTests(
  TestConfig config,
  dynamic results,
  ServerConfig serverConfig,
) async {
  print('=== 开始后端性能测试 ===');

  // 创建后端服务列表
  final services = <String, dynamic>{};

  if (!config.trailbaseOnly) {
    services['PocketBase'] = PocketBaseService(serverConfig.pocketbaseUrl);
  }

  if (!config.pocketbaseOnly) {
    services['TrailBase'] = TrailBaseServiceSimplified(
      serverConfig.trailbaseUrl,
    );
  }

  // 收集测试结果
  List<PerformanceResult> insertResults = [];
  Map<String, QueryTestResult> queryResults = {};

  // 阶段1: 插入测试
  if (config.enableInsertTest) {
    print('\n🚀 阶段1: 插入性能测试');

    final performanceTest = PerformanceTest();

    // 为插入测试创建新的服务实例
    if (!config.trailbaseOnly) {
      performanceTest.addService(PocketBaseService(serverConfig.pocketbaseUrl));
    }

    if (!config.pocketbaseOnly) {
      performanceTest.addService(
        TrailBaseServiceSimplified(serverConfig.trailbaseUrl),
      );
    }

    // 运行插入测试并收集结果
    insertResults = await performanceTest.runFullTestSuiteWithResults(
      totalRecords: config.totalRecords,
      batchSize: config.batchSize,
      concurrentLevels: config.concurrentLevels,
      intervalSeconds: config.intervalSeconds,
      config: config,
    );
  }

  // 阶段2: 查询测试
  if (config.enableQueryTest) {
    print('\n🔍 阶段2: 查询性能测试');

    final queryTest = QueryPerformanceTest();

    // 为查询测试创建新的服务实例
    if (!config.trailbaseOnly) {
      queryTest.addService(PocketBaseService(serverConfig.pocketbaseUrl));
    }

    if (!config.pocketbaseOnly) {
      queryTest.addService(
        TrailBaseServiceSimplified(serverConfig.trailbaseUrl),
      );
    }

    // 运行查询测试并收集结果
    queryResults = await queryTest.runQueryTestSuiteWithResults(config);
  }

  // 阶段3: 连接测试
  List<ConnectionTestResult> connectionResults = [];
  if (config.enableConnectionTest) {
    print('\n🔗 阶段3: 长连接测试');

    // 创建连接测试配置
    final connectionConfig = ConnectionTestConfig.fromArgs({
      'connection-levels': results['connection-levels'],
      'test-duration': results['test-duration'],
      'connection-interval': results['connection-interval'],
      'concurrent-connections': results['concurrent-connections'],
      'max-memory': results['max-memory'],
      'max-cpu': results['max-cpu'],
      'message-push-interval': results['message-push-interval'],
      'enable-pure-connection': results['enable-pure-connection'],
      'enable-heartbeat': results['enable-heartbeat'],
      'enable-message-push': results['enable-message-push'],
      'enable-connection-speed': results['enable-connection-speed'],
      'pocketbase-url': serverConfig.pocketbaseUrl,
      'trailbase-url': serverConfig.trailbaseUrl,
      'pocketbase-only': config.pocketbaseOnly,
      'trailbase-only': config.trailbaseOnly,
    });

    final connectionTest = RealtimeConnectionTest(connectionConfig);
    connectionResults = await connectionTest.runConnectionTests();
  }

  // 生成统一报告
  if (insertResults.isNotEmpty ||
      queryResults.isNotEmpty ||
      connectionResults.isNotEmpty) {
    print('\n📄 生成统一测试报告...');
    await UnifiedReportGenerator.generateUnifiedReport(
      insertResults: insertResults,
      queryResults: queryResults,
      connectionResults: connectionResults,
      config: config,
    );
  }

  print('\n✅ 所有测试阶段完成！');
}

/// 显示测试进度
void showProgress(String message) {
  stdout.write('\r$message');
}

/// 清除进度显示
void clearProgress() {
  stdout.write('\r${' ' * 80}\r');
}
