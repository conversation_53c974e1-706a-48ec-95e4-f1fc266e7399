import 'dart:io';
import 'package:args/args.dart';
import '../lib/config/vps_stress_test_config.dart';
import '../lib/tests/vps_stress_test_runner.dart';

/// VPS压力测试主程序
void main(List<String> arguments) async {
  final parser = ArgParser()
    ..addOption('config', 
        abbr: 'c', 
        help: 'VPS压力测试配置文件路径',
        defaultsTo: 'config/vps_stress_test_config.json')
    ..addOption('test-type',
        abbr: 't',
        help: '指定运行的测试类型 (write, read, websocket, all)',
        defaultsTo: 'all',
        allowed: ['write', 'read', 'websocket', 'all'])
    ..addOption('server-url',
        help: '覆盖配置文件中的服务器URL')
    ..addOption('server-name',
        help: '覆盖配置文件中的服务器名称')
    ..addOption('admin-email',
        help: '覆盖配置文件中的管理员邮箱')
    ..addOption('admin-password',
        help: '覆盖配置文件中的管理员密码')
    ..addOption('test-duration',
        help: '覆盖配置文件中的测试持续时间(分钟)')
    ..addFlag('write-test',
        help: '启用写入压力测试',
        defaultsTo: null)
    ..addFlag('read-test',
        help: '启用读取压力测试',
        defaultsTo: null)
    ..addFlag('websocket-test',
        help: '启用WebSocket压力测试',
        defaultsTo: null)
    ..addFlag('generate-markdown',
        help: '生成Markdown报告',
        defaultsTo: null)
    ..addFlag('generate-json',
        help: '生成JSON报告',
        defaultsTo: null)
    ..addFlag('generate-csv',
        help: '生成CSV报告',
        defaultsTo: null)
    ..addFlag('help',
        abbr: 'h',
        help: '显示帮助信息',
        negatable: false);

  try {
    final results = parser.parse(arguments);

    if (results['help'] as bool) {
      _printUsage(parser);
      return;
    }

    // 加载配置
    final configPath = results['config'] as String;
    print('📋 加载VPS压力测试配置: $configPath');
    
    final config = await VpsStressTestConfig.fromJsonFile(configPath);
    
    // 应用命令行参数覆盖
    _applyCommandLineOverrides(config, results);
    
    // 验证配置
    config.validate();
    
    // 打印配置信息
    config.printConfig();
    
    // 创建测试运行器
    final runner = VpsStressTestRunner(config: config);
    
    // 设置信号处理器以优雅地停止测试
    _setupSignalHandlers(runner);
    
    // 运行测试
    final testType = results['test-type'] as String;
    
    if (testType == 'all') {
      await runner.runCompleteStressTest();
    } else {
      await runner.runSingleTest(testType);
    }
    
    print('🎉 VPS压力测试完成！');
    
  } catch (e) {
    print('❌ 错误: $e');
    print('');
    _printUsage(parser);
    exit(1);
  }
}

/// 应用命令行参数覆盖
void _applyCommandLineOverrides(VpsStressTestConfig config, ArgResults results) {
  // 服务器配置覆盖
  if (results.wasParsed('server-url')) {
    config.serverUrl = results['server-url'] as String;
  }
  
  if (results.wasParsed('server-name')) {
    config.serverName = results['server-name'] as String;
  }
  
  if (results.wasParsed('admin-email')) {
    config.adminEmail = results['admin-email'] as String;
  }
  
  if (results.wasParsed('admin-password')) {
    config.adminPassword = results['admin-password'] as String;
  }
  
  if (results.wasParsed('test-duration')) {
    final duration = int.tryParse(results['test-duration'] as String);
    if (duration != null && duration > 0) {
      config.testDurationMinutes = duration;
    }
  }
  
  // 测试启用状态覆盖
  if (results.wasParsed('write-test')) {
    config.enableWriteStressTest = results['write-test'] as bool;
  }
  
  if (results.wasParsed('read-test')) {
    config.enableReadStressTest = results['read-test'] as bool;
  }
  
  if (results.wasParsed('websocket-test')) {
    config.enableWebsocketStressTest = results['websocket-test'] as bool;
  }
  
  // 报告生成覆盖
  if (results.wasParsed('generate-markdown')) {
    config.generateMarkdownReport = results['generate-markdown'] as bool;
  }
  
  if (results.wasParsed('generate-json')) {
    config.generateJsonReport = results['generate-json'] as bool;
  }
  
  if (results.wasParsed('generate-csv')) {
    config.generateCsvReport = results['generate-csv'] as bool;
  }
}

/// 设置信号处理器
void _setupSignalHandlers(VpsStressTestRunner runner) {
  // 处理 Ctrl+C (SIGINT)
  ProcessSignal.sigint.watch().listen((signal) {
    print('\n🛑 接收到中断信号，正在停止测试...');
    runner.stopAllTests();
    exit(0);
  });
  
  // 处理 SIGTERM
  ProcessSignal.sigterm.watch().listen((signal) {
    print('\n🛑 接收到终止信号，正在停止测试...');
    runner.stopAllTests();
    exit(0);
  });
}

/// 打印使用说明
void _printUsage(ArgParser parser) {
  print('VPS服务器极限压力测试工具');
  print('');
  print('用法: dart run bin/vps_stress_test.dart [选项]');
  print('');
  print('选项:');
  print(parser.usage);
  print('');
  print('示例:');
  print('  # 使用默认配置运行完整测试');
  print('  dart run bin/vps_stress_test.dart');
  print('');
  print('  # 使用自定义配置文件');
  print('  dart run bin/vps_stress_test.dart -c config/my_vps_config.json');
  print('');
  print('  # 只运行写入压力测试');
  print('  dart run bin/vps_stress_test.dart -t write');
  print('');
  print('  # 覆盖服务器URL和测试时长');
  print('  dart run bin/vps_stress_test.dart --server-url=http://my-server.com --test-duration=30');
  print('');
  print('  # 启用特定测试并生成特定格式报告');
  print('  dart run bin/vps_stress_test.dart --write-test --read-test --generate-markdown --generate-json');
  print('');
  print('测试类型说明:');
  print('  write     - 写入压力测试，测试最大并发写入用户数');
  print('  read      - 读取压力测试，测试最大并发读取用户数');
  print('  websocket - WebSocket压力测试，测试最大并发连接数');
  print('  all       - 运行所有启用的测试（默认）');
  print('');
  print('配置文件:');
  print('  配置文件使用JSON格式，包含服务器信息、测试参数和监控阈值。');
  print('  参考 config/vps_stress_test_config.json 了解完整配置选项。');
  print('');
  print('报告输出:');
  print('  测试完成后会生成详细的性能报告，包括:');
  print('  - Markdown格式的可读性报告');
  print('  - JSON格式的结构化数据');
  print('  - CSV格式的数据分析文件');
  print('');
  print('注意事项:');
  print('  - 确保目标服务器已启动并可访问');
  print('  - 测试会产生大量请求，请在测试环境中运行');
  print('  - 使用 Ctrl+C 可以优雅地停止正在运行的测试');
}
