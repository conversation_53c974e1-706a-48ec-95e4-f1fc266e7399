# 后端性能测试工具

一个用于对比PocketBase和TrailBase后端性能的综合测试工具，支持插入性能、查询性能和连接性能测试。

## 数据库表结构

```sql
CREATE TABLE IF NOT EXISTS performance_test (
  id           INTEGER PRIMARY KEY,
  name         TEXT NOT NULL,
  email        TEXT NOT NULL,
  age          INTEGER NOT NULL,
  score        REAL NOT NULL,
  created_at   INTEGER NOT NULL DEFAULT (UNIXEPOCH()),
  description  TEXT NOT NULL
) STRICT;
```

## 预设测试命令

### 1. 仅测试写入性能

```bash
# 标准写入性能测试（10万条记录）
dart run bin/main.dart --enable-insert --no-enable-query --no-enable-connection

# 大规模写入测试（50万条记录）
dart run bin/main.dart --enable-insert --no-enable-query --no-enable-connection \
  --records=500000 --batch-size=5000 --concurrency=1,2,4,8

# 高并发写入测试
dart run bin/main.dart --enable-insert --no-enable-query --no-enable-connection \
  --records=100000 --batch-size=2000 --concurrency=1,2,4,8,16

# 只测试PocketBase写入
dart run bin/main.dart --enable-insert --no-enable-query --no-enable-connection \
  --pocketbase-only

# 只测试TrailBase写入
dart run bin/main.dart --enable-insert --no-enable-query --no-enable-connection \
  --trailbase-only
```

### 2. 仅测试查询性能

```bash
# 标准查询性能测试（2000次查询）
dart run bin/main.dart --no-enable-insert --enable-query --no-enable-connection

# 高强度查询测试（10000次查询）
dart run bin/main.dart --no-enable-insert --enable-query --no-enable-connection \
  --query-iterations=10000

# 高平发查询测试
dart run bin/main.dart --no-enable-insert --enable-query --no-enable-connection --query-iterations=1000 --query-concurrency=5,10,20

# 特定查询类型测试
dart run bin/main.dart --no-enable-insert --enable-query --no-enable-connection \
  --enable-id-query --no-enable-condition-query --no-enable-range-query \
  --no-enable-pagination-query --no-enable-aggregation-query

# 只测试PocketBase查询
dart run bin/main.dart --no-enable-insert --enable-query --no-enable-connection \
  --pocketbase-only --query-iterations=5000

# 只测试TrailBase查询
dart run bin/main.dart --no-enable-insert --enable-query --no-enable-connection \
  --trailbase-only --query-iterations=5000
```

### 3. 仅测试连接性能

```bash
# 标准连接测试（5000-50000连接）
dart run bin/main.dart --no-enable-insert --no-enable-query --enable-connection

# 快速连接测试（小规模）
dart run bin/main.dart --no-enable-insert --no-enable-query --enable-connection \
  --connection-levels=1000,2000,5000 --test-duration=2

# 大规模连接测试
dart run bin/main.dart --no-enable-insert --no-enable-query --enable-connection \
  --connection-levels=5000,10000,20000,50000 --test-duration=5 \
  --concurrent-connections=200 --max-memory=6144

# 保守连接测试（低资源）
dart run bin/main.dart --no-enable-insert --no-enable-query --enable-connection \
  --connection-levels=500,1000,2000 --test-duration=3 \
  --concurrent-connections=50 --max-memory=2048 --max-cpu=70

# 只测试PocketBase连接
dart run bin/main.dart --no-enable-insert --no-enable-query --enable-connection \
  --pocketbase-only --connection-levels=5000,10000

# 只测试TrailBase连接
dart run bin/main.dart --no-enable-insert --no-enable-query --enable-connection \
  --trailbase-only --connection-levels=5000,10000
```

### 4. 测试写入+查询性能

```bash
# 标准写入+查询组合测试
dart run bin/main.dart --enable-insert --enable-query --no-enable-connection

# 大规模写入+高强度查询测试
dart run bin/main.dart --enable-insert --enable-query --no-enable-connection \
  --records=200000 --batch-size=3000 --concurrency=1,2,4,8 \
  --query-iterations=5000

# 快速写入+查询测试
dart run bin/main.dart --enable-insert --enable-query --no-enable-connection \
  --records=50000 --batch-size=2000 --concurrency=1,2,4 \
  --query-iterations=1000

# 只测试PocketBase写入+查询
dart run bin/main.dart --enable-insert --enable-query --no-enable-connection \
  --pocketbase-only --records=100000 --query-iterations=3000

# 只测试TrailBase写入+查询
dart run bin/main.dart --enable-insert --enable-query --no-enable-connection \
  --trailbase-only --records=100000 --query-iterations=3000

# 写入+特定查询类型测试
dart run bin/main.dart --enable-insert --enable-query --no-enable-connection \
  --records=100000 --enable-id-query --enable-condition-query \
  --no-enable-range-query --no-enable-pagination-query --no-enable-aggregation-query
```

### 5. 测试写入+查询+连接性能（完整测试）

```bash
# 标准完整性能测试
dart run bin/main.dart --enable-insert --enable-query --enable-connection

# 大规模完整性能测试
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=200000 --batch-size=3000 --concurrency=1,2,4,8 \
  --query-iterations=3000 \
  --connection-levels=5000,10000,20000 --test-duration=5 \
  --concurrent-connections=150 --max-memory=6144

# 快速完整测试（适合CI/CD）
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=20000 --batch-size=1000 --concurrency=1,2,4 \
  --query-iterations=500 \
  --connection-levels=500,1000,2000 --test-duration=2 \
  --concurrent-connections=100

# 高性能完整测试（高配置服务器）
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=500000 --batch-size=5000 --concurrency=1,2,4,8,16 \
  --query-iterations=10000 \
  --connection-levels=10000,20000,30000,50000 --test-duration=10 \
  --concurrent-connections=200 --max-memory=8192 --max-cpu=90

# 保守完整测试（低配置服务器）
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=50000 --batch-size=1000 --concurrency=1,2 \
  --query-iterations=1000 \
  --connection-levels=1000,2000,3000 --test-duration=3 \
  --concurrent-connections=50 --max-memory=2048 --max-cpu=70

# 只测试PocketBase完整性能
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --pocketbase-only \
  --records=100000 --query-iterations=2000 \
  --connection-levels=5000,10000

# 只测试TrailBase完整性能
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --trailbase-only \
  --records=100000 --query-iterations=2000 \
  --connection-levels=5000,10000
```

## 主要参数说明

### 阶段控制参数
- `--enable-insert` / `--no-enable-insert`: 启用/禁用插入测试
- `--enable-query` / `--no-enable-query`: 启用/禁用查询测试
- `--enable-connection` / `--no-enable-connection`: 启用/禁用连接测试

### 插入测试参数
- `--records=N`: 插入记录数量（默认：100000）
- `--batch-size=N`: 批量插入大小（默认：2000）
- `--concurrency=1,2,4,8`: 并发级别（默认：1,2,4,8）

### 查询测试参数
- `--query-iterations=N`: 查询迭代次数（默认：2000）
- `--query-batch-size=N`: 查询批次大小（默认：100）
- `--enable-id-query`: 启用ID查询测试
- `--enable-condition-query`: 启用条件查询测试
- `--enable-range-query`: 启用范围查询测试
- `--enable-pagination-query`: 启用分页查询测试
- `--enable-aggregation-query`: 启用聚合查询测试

### 连接测试参数
- `--connection-levels=5000,10000,20000`: 连接数级别（默认：5000,10000,15000,20000,30000,50000）
- `--test-duration=N`: 每级别测试时间（分钟，默认：10）
- `--concurrent-connections=N`: 并发连接数（默认：100）
- `--connection-interval=N`: 连接建立间隔（毫秒，默认：5）
- `--max-memory=N`: 最大内存使用限制（MB，默认：4096）
- `--max-cpu=N`: 最大CPU使用率限制（%，默认：90）

### 后端选择参数
- `--pocketbase-only`: 只测试PocketBase
- `--trailbase-only`: 只测试TrailBase
- `--pocketbase-url=URL`: PocketBase服务器URL（默认：http://0.0.0.0:8090）
- `--trailbase-url=URL`: TrailBase服务器URL（默认：http://0.0.0.0:4000）

### 报告生成参数
- `--generate-json`: 生成JSON报告
- `--generate-csv`: 生成CSV报告
- `--generate-md`: 生成Markdown报告（默认启用）

## 使用建议

### 🚀 快速开始
```bash
# 查看所有可用参数
dart run bin/main.dart --help

# 运行快速测试（适合初次使用）
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=10000 --query-iterations=500 \
  --connection-levels=500,1000 --test-duration=1
```

### 📊 性能基准测试
```bash
# 推荐的标准性能基准测试
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=100000 --batch-size=2000 --concurrency=1,2,4,8 \
  --query-iterations=2000 \
  --connection-levels=5000,10000,15000 --test-duration=5
```

### ⚡ 高性能服务器测试
```bash
# 适合高配置服务器的压力测试
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=500000 --batch-size=5000 --concurrency=1,2,4,8,16 \
  --query-iterations=10000 \
  --connection-levels=10000,20000,30000,50000 --test-duration=10 \
  --concurrent-connections=200 --max-memory=8192
```

### 💻 低配置服务器测试
```bash
# 适合低配置服务器的保守测试
dart run bin/main.dart --enable-insert --enable-query --enable-connection \
  --records=20000 --batch-size=1000 --concurrency=1,2 \
  --query-iterations=500 \
  --connection-levels=500,1000,2000 --test-duration=2 \
  --concurrent-connections=50 --max-memory=1024 --max-cpu=70
```

## 测试报告

测试完成后会自动生成详细的性能对比报告，包括：
- 插入性能对比（记录数/秒、成功率）
- 查询性能对比（查询数/秒、延迟统计）
- 连接性能对比（连接成功率、稳定性、延迟）
- 综合性能总结和推荐建议

报告文件格式：`performance_test_results_[timestamp].md`



// 在浏览器控制台中执行此代码作为临时修复                          
if (!crypto.randomUUID) {                                          
  crypto.randomUUID = function() {                                 
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, 
function(c) {                                                      
      const r = Math.random() * 16 | 0;                            
      const v = c == 'x' ? r : (r & 0x3 | 0x8);                    
      return v.toString(16);                                       
    });                                                            
  };                                                               
} 