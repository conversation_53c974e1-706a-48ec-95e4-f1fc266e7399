import 'dart:convert';
import 'dart:io';

/// 服务器配置类
class ServerConfig {
  final String pocketbaseUrl;
  final String trailbaseUrl;
  final String environment;
  final int timeoutSeconds;
  final int retryAttempts;
  final int connectionPoolSize;

  ServerConfig({
    required this.pocketbaseUrl,
    required this.trailbaseUrl,
    required this.environment,
    this.timeoutSeconds = 30,
    this.retryAttempts = 3,
    this.connectionPoolSize = 50,
  });

  /// 从配置文件加载服务器配置
  static Future<ServerConfig> loadFromFile({
    String configPath = 'config/servers.json',
    String? environment,
    String? pocketbaseUrl,
    String? trailbaseUrl,
  }) async {
    try {
      final file = File(configPath);
      if (!await file.exists()) {
        print('⚠️ 配置文件不存在: $configPath，使用默认配置');
        return _getDefaultConfig(
          environment: environment,
          pocketbaseUrl: pocketbaseUrl,
          trailbaseUrl: trailbaseUrl,
        );
      }

      final content = await file.readAsString();
      final config = jsonDecode(content) as Map<String, dynamic>;
      
      final servers = config['servers'] as Map<String, dynamic>;
      final defaultEnv = config['default_environment'] as String? ?? 'vps';
      final connectionSettings = config['connection_settings'] as Map<String, dynamic>? ?? {};
      
      // 使用指定环境或默认环境
      final env = environment ?? defaultEnv;
      final envConfig = servers[env] as Map<String, dynamic>?;
      
      if (envConfig == null) {
        print('⚠️ 环境配置不存在: $env，使用默认配置');
        return _getDefaultConfig(
          environment: environment,
          pocketbaseUrl: pocketbaseUrl,
          trailbaseUrl: trailbaseUrl,
        );
      }

      final pocketbaseConfig = envConfig['pocketbase'] as Map<String, dynamic>?;
      final trailbaseConfig = envConfig['trailbase'] as Map<String, dynamic>?;

      return ServerConfig(
        pocketbaseUrl: pocketbaseUrl ?? pocketbaseConfig?['url'] ?? 'http://117.72.60.131:8090',
        trailbaseUrl: trailbaseUrl ?? trailbaseConfig?['url'] ?? 'http://117.72.60.131:4001',
        environment: env,
        timeoutSeconds: connectionSettings['timeout_seconds'] ?? 30,
        retryAttempts: connectionSettings['retry_attempts'] ?? 3,
        connectionPoolSize: connectionSettings['connection_pool_size'] ?? 50,
      );
    } catch (e) {
      print('⚠️ 加载配置文件失败: $e，使用默认配置');
      return _getDefaultConfig(
        environment: environment,
        pocketbaseUrl: pocketbaseUrl,
        trailbaseUrl: trailbaseUrl,
      );
    }
  }

  /// 获取默认配置
  static ServerConfig _getDefaultConfig({
    String? environment,
    String? pocketbaseUrl,
    String? trailbaseUrl,
  }) {
    return ServerConfig(
      pocketbaseUrl: pocketbaseUrl ?? 'http://117.72.60.131:8090',
      trailbaseUrl: trailbaseUrl ?? 'http://117.72.60.131:4001',
      environment: environment ?? 'vps',
    );
  }

  /// 打印配置信息
  void printConfig() {
    print('=== 服务器配置 ===');
    print('环境: $environment');
    print('PocketBase URL: $pocketbaseUrl');
    print('TrailBase URL: $trailbaseUrl');
    print('连接超时: ${timeoutSeconds}秒');
    print('重试次数: $retryAttempts');
    print('连接池大小: $connectionPoolSize');
    print('');
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'pocketbaseUrl': pocketbaseUrl,
      'trailbaseUrl': trailbaseUrl,
      'environment': environment,
      'timeoutSeconds': timeoutSeconds,
      'retryAttempts': retryAttempts,
      'connectionPoolSize': connectionPoolSize,
    };
  }
}
