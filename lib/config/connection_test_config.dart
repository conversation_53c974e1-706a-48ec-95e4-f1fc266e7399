/// 连接测试配置
class ConnectionTestConfig {
  // 测试连接数级别
  final List<int> connectionLevels;
  
  // 每个级别的测试时间（分钟）
  final int testDurationMinutes;
  
  // 连接建立间隔（毫秒）
  final int connectionIntervalMs;

  // 并发连接数
  final int concurrentConnections;
  
  // 消息推送间隔（秒）
  final int messagePushIntervalSeconds;
  
  // 健康检查间隔（秒）
  final int healthCheckIntervalSeconds;
  
  // 服务器URL配置
  final String pocketbaseUrl;
  final String trailbaseUrl;
  
  // 安全保护配置
  final double maxCpuUsagePercent;
  final double maxMemoryUsageMB;
  final int maxConsecutiveFailures;
  
  // 测试场景配置
  final bool enablePureConnection;      // 纯长连接保持
  final bool enableHeartbeat;           // 长连接+心跳
  final bool enableMessagePush;         // 长连接+实时推送
  final bool enableConnectionSpeed;     // 连接建立速度测试
  
  // 只测试特定后端
  final bool pocketbaseOnly;
  final bool trailbaseOnly;

  const ConnectionTestConfig({
    this.connectionLevels = const [5000, 10000, 15000, 20000, 30000, 50000],
    this.testDurationMinutes = 10,
    this.connectionIntervalMs = 5,
    this.concurrentConnections = 100,
    this.messagePushIntervalSeconds = 5,
    this.healthCheckIntervalSeconds = 10,
    this.pocketbaseUrl = 'http://0.0.0.0:8090',
    this.trailbaseUrl = 'http://0.0.0.0:4000',
    this.maxCpuUsagePercent = 90.0,
    this.maxMemoryUsageMB = 4096.0,
    this.maxConsecutiveFailures = 3,
    this.enablePureConnection = true,
    this.enableHeartbeat = false,
    this.enableMessagePush = true,
    this.enableConnectionSpeed = false,
    this.pocketbaseOnly = false,
    this.trailbaseOnly = false,
  });

  /// 从命令行参数创建配置
  factory ConnectionTestConfig.fromArgs(Map<String, dynamic> args) {
    // 解析连接数级别
    List<int> levels = const [5000, 10000, 15000, 20000, 30000, 50000];
    if (args['connection-levels'] != null) {
      final levelStr = args['connection-levels'] as String;
      levels = levelStr.split(',').map((s) => int.parse(s.trim())).toList();
    }

    return ConnectionTestConfig(
      connectionLevels: levels,
      testDurationMinutes: int.parse(args['test-duration'] ?? '10'),
      connectionIntervalMs: int.parse(args['connection-interval'] ?? '5'),
      concurrentConnections: int.parse(args['concurrent-connections'] ?? '100'),
      messagePushIntervalSeconds: int.parse(args['message-push-interval'] ?? '5'),
      healthCheckIntervalSeconds: int.parse(args['health-check-interval'] ?? '10'),
      pocketbaseUrl: args['pocketbase-url'] ?? 'http://0.0.0.0:8090',
      trailbaseUrl: args['trailbase-url'] ?? 'http://0.0.0.0:4000',
      maxCpuUsagePercent: double.parse(args['max-cpu'] ?? '90.0'),
      maxMemoryUsageMB: double.parse(args['max-memory'] ?? '4096.0'),
      maxConsecutiveFailures: int.parse(args['max-failures'] ?? '3'),
      enablePureConnection: args['enable-pure-connection'] ?? true,
      enableHeartbeat: args['enable-heartbeat'] ?? false,
      enableMessagePush: args['enable-message-push'] ?? true,
      enableConnectionSpeed: args['enable-connection-speed'] ?? false,
      pocketbaseOnly: args['pocketbase-only'] ?? false,
      trailbaseOnly: args['trailbase-only'] ?? false,
    );
  }

  /// 获取测试持续时间
  Duration get testDuration => Duration(minutes: testDurationMinutes);

  /// 获取连接建立间隔
  Duration get connectionInterval => Duration(milliseconds: connectionIntervalMs);

  /// 获取消息推送间隔
  Duration get messagePushInterval => Duration(seconds: messagePushIntervalSeconds);

  /// 获取健康检查间隔
  Duration get healthCheckInterval => Duration(seconds: healthCheckIntervalSeconds);

  /// 获取需要测试的后端列表
  List<String> get enabledBackends {
    if (pocketbaseOnly) return ['PocketBase'];
    if (trailbaseOnly) return ['TrailBase'];
    return ['PocketBase', 'TrailBase'];
  }

  /// 获取启用的测试场景
  List<String> get enabledScenarios {
    final scenarios = <String>[];
    if (enablePureConnection) scenarios.add('pure_connection');
    if (enableHeartbeat) scenarios.add('heartbeat');
    if (enableMessagePush) scenarios.add('message_push');
    if (enableConnectionSpeed) scenarios.add('connection_speed');
    return scenarios;
  }

  /// 打印配置信息
  void printConfig() {
    print('=== 连接测试配置 ===');
    print('连接数级别: ${connectionLevels.join(' → ')}');
    print('每级别测试时间: ${testDurationMinutes}分钟');
    print('连接建立间隔: ${connectionIntervalMs}ms');
    print('并发连接数: $concurrentConnections');
    print('消息推送间隔: ${messagePushIntervalSeconds}秒');
    print('');
    print('服务器配置:');
    print('  PocketBase URL: $pocketbaseUrl');
    print('  TrailBase URL: $trailbaseUrl');
    print('');
    print('安全保护:');
    print('  最大CPU使用率: ${maxCpuUsagePercent}%');
    print('  最大内存使用: ${maxMemoryUsageMB.toInt()}MB');
    print('  最大连续失败: $maxConsecutiveFailures次');
    print('');
    print('测试场景:');
    print('  纯长连接: $enablePureConnection');
    print('  心跳测试: $enableHeartbeat');
    print('  消息推送: $enableMessagePush');
    print('  连接速度: $enableConnectionSpeed');
    print('');
    print('后端选择:');
    print('  启用的后端: ${enabledBackends.join(', ')}');
    print('');
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'connectionLevels': connectionLevels,
      'testDurationMinutes': testDurationMinutes,
      'connectionIntervalMs': connectionIntervalMs,
      'messagePushIntervalSeconds': messagePushIntervalSeconds,
      'healthCheckIntervalSeconds': healthCheckIntervalSeconds,
      'pocketbaseUrl': pocketbaseUrl,
      'trailbaseUrl': trailbaseUrl,
      'maxCpuUsagePercent': maxCpuUsagePercent,
      'maxMemoryUsageMB': maxMemoryUsageMB,
      'maxConsecutiveFailures': maxConsecutiveFailures,
      'enablePureConnection': enablePureConnection,
      'enableHeartbeat': enableHeartbeat,
      'enableMessagePush': enableMessagePush,
      'enableConnectionSpeed': enableConnectionSpeed,
      'pocketbaseOnly': pocketbaseOnly,
      'trailbaseOnly': trailbaseOnly,
      'enabledBackends': enabledBackends,
      'enabledScenarios': enabledScenarios,
    };
  }
}
