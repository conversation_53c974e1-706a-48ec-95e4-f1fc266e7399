import 'dart:io';
import 'dart:convert';
import 'dart:math';

/// VPS压力测试配置类
class VpsStressTestConfig {
  // 测试基本设置
  int testDurationMinutes;
  final int rampUpDurationMinutes;
  final int coolDownDurationMinutes;
  final double successRateThreshold;
  final double responseTimeThresholdMs;
  final int timeoutThresholdMs;

  // 写入压力测试配置
  final bool writeTestEnabled;
  final int writeInitialUsers;
  final int writeMaxUsers;
  final int writeUserIncrementStep;
  final int writeIncrementIntervalSeconds;
  final int recordsPerUser;
  final List<int> userActionIntervalMs;
  final int batchSize;
  final String testDataSize;

  // 读取压力测试配置
  final bool readTestEnabled;
  final int readInitialUsers;
  final int readMaxUsers;
  final int readUserIncrementStep;
  final int readIncrementIntervalSeconds;
  final int queriesPerUser;
  final List<int> readActionIntervalMs;
  final List<String> queryTypes;
  final String queryComplexity;

  // WebSocket压力测试配置
  final bool websocketTestEnabled;
  final int websocketInitialConnections;
  final int websocketMaxConnections;
  final int websocketIncrementStep;
  final int websocketIncrementIntervalSeconds;
  final int heartbeatIntervalSeconds;
  final List<int> messageFrequencySeconds;
  final int connectionHoldDurationMinutes;
  final int messageSizeBytes;

  // 监控配置
  final int metricsCollectionIntervalSeconds;
  final int performanceCheckIntervalSeconds;
  final bool autoStopOnFailure;
  final int failureThresholdConsecutive;
  final int memoryUsageThresholdMb;
  final int cpuUsageThresholdPercent;

  // 服务器配置
  String serverName;
  String serverUrl;
  String serverType;
  String adminEmail;
  String adminPassword;
  String testTableName;
  int initialDataCount;
  final String pocketbaseUrl;
  final String trailbaseUrl;
  final bool testBothServers;
  final int connectionTimeoutSeconds;
  final int requestTimeoutSeconds;

  // 报告配置
  bool generateMarkdownReport;
  bool generateJsonReport;
  bool generateCsvReport;
  final bool generateRealTimeCharts;
  final bool saveDetailedLogs;
  final bool exportCsvData;
  final bool generatePerformanceCurves;
  final bool includeRecommendations;

  // 测试启用状态
  bool enableWriteStressTest;
  bool enableReadStressTest;
  bool enableWebsocketStressTest;

  VpsStressTestConfig({
    // 测试基本设置
    this.testDurationMinutes = 30,
    this.rampUpDurationMinutes = 5,
    this.coolDownDurationMinutes = 2,
    this.successRateThreshold = 0.95,
    this.responseTimeThresholdMs = 5000.0,
    this.timeoutThresholdMs = 10000,

    // 写入压力测试配置
    this.writeTestEnabled = true,
    this.writeInitialUsers = 1,
    this.writeMaxUsers = 1000,
    this.writeUserIncrementStep = 5,
    this.writeIncrementIntervalSeconds = 30,
    this.recordsPerUser = 10,
    this.userActionIntervalMs = const [500, 2000],
    this.batchSize = 1,
    this.testDataSize = 'medium',

    // 读取压力测试配置
    this.readTestEnabled = true,
    this.readInitialUsers = 5,
    this.readMaxUsers = 2000,
    this.readUserIncrementStep = 10,
    this.readIncrementIntervalSeconds = 20,
    this.queriesPerUser = 20,
    this.readActionIntervalMs = const [200, 1000],
    this.queryTypes = const ['id', 'condition', 'range', 'pagination'],
    this.queryComplexity = 'mixed',

    // WebSocket压力测试配置
    this.websocketTestEnabled = true,
    this.websocketInitialConnections = 10,
    this.websocketMaxConnections = 5000,
    this.websocketIncrementStep = 50,
    this.websocketIncrementIntervalSeconds = 15,
    this.heartbeatIntervalSeconds = 30,
    this.messageFrequencySeconds = const [5, 15],
    this.connectionHoldDurationMinutes = 10,
    this.messageSizeBytes = 256,

    // 监控配置
    this.metricsCollectionIntervalSeconds = 5,
    this.performanceCheckIntervalSeconds = 10,
    this.autoStopOnFailure = true,
    this.failureThresholdConsecutive = 3,
    this.memoryUsageThresholdMb = 8192,
    this.cpuUsageThresholdPercent = 90,

    // 服务器配置
    this.serverName = 'VPS服务器',
    this.serverUrl = 'http://117.72.60.131:8090',
    this.serverType = 'pocketbase',
    this.adminEmail = '<EMAIL>',
    this.adminPassword = 'admin123456',
    this.testTableName = 'vps_stress_test',
    this.initialDataCount = 1000,
    this.pocketbaseUrl = 'http://117.72.60.131:8090',
    this.trailbaseUrl = 'http://117.72.60.131:4000',
    this.testBothServers = true,
    this.connectionTimeoutSeconds = 30,
    this.requestTimeoutSeconds = 15,

    // 报告配置
    this.generateMarkdownReport = true,
    this.generateJsonReport = true,
    this.generateCsvReport = true,
    this.generateRealTimeCharts = true,
    this.saveDetailedLogs = true,
    this.exportCsvData = true,
    this.generatePerformanceCurves = true,
    this.includeRecommendations = true,

    // 测试启用状态
    this.enableWriteStressTest = true,
    this.enableReadStressTest = true,
    this.enableWebsocketStressTest = true,
  });

  /// 从JSON配置文件创建配置
  factory VpsStressTestConfig.fromJsonFile(String configPath) {
    try {
      final file = File(configPath);
      if (!file.existsSync()) {
        print('VPS压力测试配置文件不存在: $configPath，使用默认配置');
        return VpsStressTestConfig();
      }

      final jsonString = file.readAsStringSync();
      final jsonData = json.decode(jsonString) as Map<String, dynamic>;

      return VpsStressTestConfig(
          // 测试基本设置
          testDurationMinutes:
              jsonData['test_settings']?['test_duration_minutes'] ?? 30,
          rampUpDurationMinutes:
              jsonData['test_settings']?['ramp_up_duration_minutes'] ?? 5,
          coolDownDurationMinutes:
              jsonData['test_settings']?['cool_down_duration_minutes'] ?? 2,
          successRateThreshold:
              (jsonData['test_settings']?['success_rate_threshold'] ?? 0.95)
                  .toDouble(),
          responseTimeThresholdMs:
              jsonData['test_settings']?['response_time_threshold_ms'] ?? 5000,
          timeoutThresholdMs:
              jsonData['test_settings']?['timeout_threshold_ms'] ?? 10000,

          // 写入压力测试配置
          writeTestEnabled: jsonData['write_stress_test']?['enabled'] ?? true,
          writeInitialUsers:
              jsonData['write_stress_test']?['initial_users'] ?? 1,
          writeMaxUsers: jsonData['write_stress_test']?['max_users'] ?? 1000,
          writeUserIncrementStep:
              jsonData['write_stress_test']?['user_increment_step'] ?? 5,
          writeIncrementIntervalSeconds:
              jsonData['write_stress_test']?['increment_interval_seconds'] ??
              30,
          recordsPerUser:
              jsonData['write_stress_test']?['records_per_user'] ?? 10,
          userActionIntervalMs: _parseIntListFromJson(
            jsonData['write_stress_test']?['user_action_interval_ms'],
            [500, 2000],
          ),
          batchSize: jsonData['write_stress_test']?['batch_size'] ?? 1,
          testDataSize:
              jsonData['write_stress_test']?['test_data_size'] ?? 'medium',

          // 读取压力测试配置
          readTestEnabled: jsonData['read_stress_test']?['enabled'] ?? true,
          readInitialUsers: jsonData['read_stress_test']?['initial_users'] ?? 5,
          readMaxUsers: jsonData['read_stress_test']?['max_users'] ?? 2000,
          readUserIncrementStep:
              jsonData['read_stress_test']?['user_increment_step'] ?? 10,
          readIncrementIntervalSeconds:
              jsonData['read_stress_test']?['increment_interval_seconds'] ?? 20,
          queriesPerUser:
              jsonData['read_stress_test']?['queries_per_user'] ?? 20,
          readActionIntervalMs: _parseIntListFromJson(
            jsonData['read_stress_test']?['user_action_interval_ms'],
            [200, 1000],
          ),
          queryTypes: _parseStringListFromJson(
            jsonData['read_stress_test']?['query_types'],
            ['id', 'condition', 'range', 'pagination'],
          ),
          queryComplexity:
              jsonData['read_stress_test']?['query_complexity'] ?? 'mixed',

          // WebSocket压力测试配置
          websocketTestEnabled:
              jsonData['websocket_stress_test']?['enabled'] ?? true,
          websocketInitialConnections:
              jsonData['websocket_stress_test']?['initial_connections'] ?? 10,
          websocketMaxConnections:
              jsonData['websocket_stress_test']?['max_connections'] ?? 5000,
          websocketIncrementStep:
              jsonData['websocket_stress_test']?['connection_increment_step'] ??
              50,
          websocketIncrementIntervalSeconds:
              jsonData['websocket_stress_test']?['increment_interval_seconds'] ??
              15,
          heartbeatIntervalSeconds:
              jsonData['websocket_stress_test']?['heartbeat_interval_seconds'] ??
              30,
          messageFrequencySeconds: _parseIntListFromJson(
            jsonData['websocket_stress_test']?['message_frequency_seconds'],
            [5, 15],
          ),
          connectionHoldDurationMinutes:
              jsonData['websocket_stress_test']?['connection_hold_duration_minutes'] ??
              10,
          messageSizeBytes:
              jsonData['websocket_stress_test']?['message_size_bytes'] ?? 256,

          // 监控配置
          metricsCollectionIntervalSeconds:
              jsonData['monitoring']?['metrics_collection_interval_seconds'] ??
              5,
          performanceCheckIntervalSeconds:
              jsonData['monitoring']?['performance_check_interval_seconds'] ??
              10,
          autoStopOnFailure:
              jsonData['monitoring']?['auto_stop_on_failure'] ?? true,
          failureThresholdConsecutive:
              jsonData['monitoring']?['failure_threshold_consecutive'] ?? 3,
          memoryUsageThresholdMb:
              jsonData['monitoring']?['memory_usage_threshold_mb'] ?? 8192,
          cpuUsageThresholdPercent:
              jsonData['monitoring']?['cpu_usage_threshold_percent'] ?? 90,

          // 服务器配置
          pocketbaseUrl:
              jsonData['server_config']?['pocketbase_url'] ??
              'http://117.72.60.131:8090',
          trailbaseUrl:
              jsonData['server_config']?['trailbase_url'] ??
              'http://117.72.60.131:4000',
          testBothServers:
              jsonData['server_config']?['test_both_servers'] ?? true,
          connectionTimeoutSeconds:
              jsonData['server_config']?['connection_timeout_seconds'] ?? 30,
          requestTimeoutSeconds:
              jsonData['server_config']?['request_timeout_seconds'] ?? 15,

          // 报告配置
          generateRealTimeCharts:
              jsonData['reporting']?['generate_real_time_charts'] ?? true,
          saveDetailedLogs:
              jsonData['reporting']?['save_detailed_logs'] ?? true,
          exportCsvData: jsonData['reporting']?['export_csv_data'] ?? true,
          generatePerformanceCurves:
              jsonData['reporting']?['generate_performance_curves'] ?? true,
          includeRecommendations:
              jsonData['reporting']?['include_recommendations'] ?? true,
        )
        // 设置测试启用状态
        ..enableWriteStressTest =
            jsonData['write_stress_test']?['enabled'] ?? true
        ..enableReadStressTest =
            jsonData['read_stress_test']?['enabled'] ?? true
        ..enableWebsocketStressTest =
            jsonData['websocket_stress_test']?['enabled'] ?? true
        // 设置服务器配置
        ..serverType = jsonData['server_config']?['server_type'] ?? 'pocketbase'
        ..serverName = jsonData['server_config']?['server_name'] ?? 'VPS服务器'
        ..serverUrl = jsonData['server_config']?['server_url'] ??
            (jsonData['server_config']?['server_type'] == 'trailbase'
                ? jsonData['server_config']?['trailbase_url'] ?? 'http://117.72.60.131:4000'
                : jsonData['server_config']?['pocketbase_url'] ?? 'http://117.72.60.131:8090')
        ..adminEmail = jsonData['server_config']?['admin_email'] ?? '<EMAIL>'
        ..adminPassword = jsonData['server_config']?['admin_password'] ?? 'admin123456';
    } catch (e) {
      print('解析VPS压力测试配置文件失败: $e，使用默认配置');
      return VpsStressTestConfig();
    }
  }

  /// 从JSON解析整数列表
  static List<int> _parseIntListFromJson(
    dynamic value,
    List<int> defaultValue,
  ) {
    if (value == null) return defaultValue;
    if (value is List) {
      try {
        return value
            .map((e) => e is int ? e : int.parse(e.toString()))
            .toList();
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// 从JSON解析字符串列表
  static List<String> _parseStringListFromJson(
    dynamic value,
    List<String> defaultValue,
  ) {
    if (value == null) return defaultValue;
    if (value is List) {
      try {
        return value.map((e) => e.toString()).toList();
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// 获取随机用户操作间隔（写入测试）
  int getRandomWriteActionInterval() {
    final random = Random();
    final min = userActionIntervalMs[0];
    final max = userActionIntervalMs.length > 1 ? userActionIntervalMs[1] : min;
    return min + random.nextInt(max - min + 1);
  }

  /// 获取随机用户操作间隔（读取测试）
  int getRandomReadActionInterval() {
    final random = Random();
    final min = readActionIntervalMs[0];
    final max = readActionIntervalMs.length > 1 ? readActionIntervalMs[1] : min;
    return min + random.nextInt(max - min + 1);
  }

  /// 获取随机消息频率间隔（WebSocket测试）
  int getRandomMessageFrequency() {
    final random = Random();
    final min = messageFrequencySeconds[0];
    final max = messageFrequencySeconds.length > 1
        ? messageFrequencySeconds[1]
        : min;
    return min + random.nextInt(max - min + 1);
  }

  /// 打印配置信息
  void printConfig() {
    print('=== VPS压力测试配置 ===');
    print('测试基本设置:');
    print('  测试持续时间: ${testDurationMinutes}分钟');
    print('  预热时间: ${rampUpDurationMinutes}分钟');
    print('  冷却时间: ${coolDownDurationMinutes}分钟');
    print('  成功率阈值: ${(successRateThreshold * 100).toStringAsFixed(1)}%');
    print('  响应时间阈值: ${responseTimeThresholdMs}ms');
    print('  超时阈值: ${timeoutThresholdMs}ms');
    print('');

    if (writeTestEnabled) {
      print('写入压力测试:');
      print('  初始用户数: $writeInitialUsers');
      print('  最大用户数: $writeMaxUsers');
      print('  用户递增步长: $writeUserIncrementStep');
      print('  递增间隔: ${writeIncrementIntervalSeconds}秒');
      print('  每用户记录数: $recordsPerUser');
      print('  用户操作间隔: ${userActionIntervalMs}ms');
      print('');
    }

    if (readTestEnabled) {
      print('读取压力测试:');
      print('  初始用户数: $readInitialUsers');
      print('  最大用户数: $readMaxUsers');
      print('  用户递增步长: $readUserIncrementStep');
      print('  递增间隔: ${readIncrementIntervalSeconds}秒');
      print('  每用户查询数: $queriesPerUser');
      print('  查询类型: $queryTypes');
      print('');
    }

    if (websocketTestEnabled) {
      print('WebSocket压力测试:');
      print('  初始连接数: $websocketInitialConnections');
      print('  最大连接数: $websocketMaxConnections');
      print('  连接递增步长: $websocketIncrementStep');
      print('  递增间隔: ${websocketIncrementIntervalSeconds}秒');
      print('  连接保持时间: ${connectionHoldDurationMinutes}分钟');
      print('');
    }

    print('服务器配置:');
    print('  PocketBase URL: $pocketbaseUrl');
    print('  TrailBase URL: $trailbaseUrl');
    print('  测试两个服务器: $testBothServers');
    print('');
  }

  /// 验证配置
  bool validate() {
    if (!writeTestEnabled && !readTestEnabled && !websocketTestEnabled) {
      print('错误: 至少需要启用一种压力测试');
      return false;
    }

    if (testDurationMinutes <= 0) {
      print('错误: 测试持续时间必须大于0');
      return false;
    }

    if (successRateThreshold <= 0 || successRateThreshold > 1) {
      print('错误: 成功率阈值必须在0-1之间');
      return false;
    }

    if (writeTestEnabled) {
      if (writeMaxUsers <= writeInitialUsers) {
        print('错误: 写入测试最大用户数必须大于初始用户数');
        return false;
      }
      if (writeUserIncrementStep <= 0) {
        print('错误: 写入测试用户递增步长必须大于0');
        return false;
      }
    }

    if (readTestEnabled) {
      if (readMaxUsers <= readInitialUsers) {
        print('错误: 读取测试最大用户数必须大于初始用户数');
        return false;
      }
      if (readUserIncrementStep <= 0) {
        print('错误: 读取测试用户递增步长必须大于0');
        return false;
      }
    }

    if (websocketTestEnabled) {
      if (websocketMaxConnections <= websocketInitialConnections) {
        print('错误: WebSocket测试最大连接数必须大于初始连接数');
        return false;
      }
      if (websocketIncrementStep <= 0) {
        print('错误: WebSocket测试连接递增步长必须大于0');
        return false;
      }
    }

    return true;
  }
}
