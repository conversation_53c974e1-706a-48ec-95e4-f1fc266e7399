import 'dart:io';
import 'dart:convert';

/// 测试配置类
class TestConfig {
  // 阶段控制
  final bool enableInsertTest;
  final bool enableQueryTest;
  final bool enableConnectionTest;

  // 插入测试类型控制
  final bool enableSingleInsert;
  final bool enableBatchInsert;
  final bool enableConcurrentInsert;

  // 插入测试配置
  final int totalRecords;
  final int batchSize;
  final List<int> concurrentLevels;
  final int intervalSeconds;

  // 查询测试配置
  final int queryIterations;
  final int queryBatchSize;
  final bool enableIdQuery;
  final bool enableConditionQuery;
  final bool enableRangeQuery;
  final bool enablePaginationQuery;
  final bool enableAggregationQuery;
  final List<int> queryConcurrencyLevels;

  // 服务器选择配置
  final bool pocketbaseOnly;
  final bool trailbaseOnly;

  // 报告配置
  final bool generateJsonReport;
  final bool generateCsvReport;
  final bool generateMdReport;

  const TestConfig({
    // 阶段控制
    this.enableInsertTest = true,
    this.enableQueryTest = false,
    this.enableConnectionTest = false,

    // 插入测试类型控制
    this.enableSingleInsert = true,
    this.enableBatchInsert = true,
    this.enableConcurrentInsert = true,

    // 插入测试配置
    this.totalRecords = 100000,
    this.batchSize = 2000,
    this.concurrentLevels = const [1, 2, 4, 8],
    this.intervalSeconds = 5,

    // 查询测试配置
    this.queryIterations = 1000,
    this.queryBatchSize = 100,
    this.enableIdQuery = true,
    this.enableConditionQuery = true,
    this.enableRangeQuery = true,
    this.enablePaginationQuery = true,
    this.enableAggregationQuery = true,
    this.queryConcurrencyLevels = const [5, 10, 20],

    // 服务器选择配置
    this.pocketbaseOnly = false,
    this.trailbaseOnly = false,

    // 报告配置
    this.generateJsonReport = false,
    this.generateCsvReport = false,
    this.generateMdReport = true,
  });

  /// 从JSON配置文件创建配置
  factory TestConfig.fromJsonFile(String configPath) {
    try {
      final file = File(configPath);
      if (!file.existsSync()) {
        print('配置文件不存在: $configPath，使用默认配置');
        return const TestConfig();
      }

      final jsonString = file.readAsStringSync();
      final jsonData = json.decode(jsonString) as Map<String, dynamic>;

      return TestConfig(
        // 阶段控制
        enableInsertTest:
            jsonData['test_stages']?['enable_insert_test'] ?? true,
        enableQueryTest: jsonData['test_stages']?['enable_query_test'] ?? false,
        enableConnectionTest:
            jsonData['test_stages']?['enable_connection_test'] ?? false,

        // 插入测试类型控制
        enableSingleInsert:
            jsonData['insert_tests']?['enable_single_insert'] ?? true,
        enableBatchInsert:
            jsonData['insert_tests']?['enable_batch_insert'] ?? true,
        enableConcurrentInsert:
            jsonData['insert_tests']?['enable_concurrent_insert'] ?? true,

        // 插入测试配置
        totalRecords: jsonData['test_parameters']?['total_records'] ?? 100000,
        batchSize: jsonData['test_parameters']?['batch_size'] ?? 2000,
        concurrentLevels: _parseIntListFromJson(
          jsonData['test_parameters']?['concurrent_levels'],
          [1, 2, 4, 8],
        ),
        intervalSeconds: jsonData['test_parameters']?['interval_seconds'] ?? 10,

        // 查询测试配置
        queryIterations: jsonData['query_tests']?['query_iterations'] ?? 1000,
        queryBatchSize: jsonData['query_tests']?['query_batch_size'] ?? 100,
        enableIdQuery: jsonData['query_tests']?['enable_id_query'] ?? true,
        enableConditionQuery:
            jsonData['query_tests']?['enable_condition_query'] ?? true,
        enableRangeQuery:
            jsonData['query_tests']?['enable_range_query'] ?? true,
        enablePaginationQuery:
            jsonData['query_tests']?['enable_pagination_query'] ?? true,
        enableAggregationQuery:
            jsonData['query_tests']?['enable_aggregation_query'] ?? true,
        queryConcurrencyLevels: _parseIntListFromJson(
          jsonData['query_tests']?['query_concurrency_levels'],
          [5, 10, 20],
        ),

        // 服务器选择配置
        pocketbaseOnly:
            jsonData['server_selection']?['pocketbase_only'] ?? false,
        trailbaseOnly: jsonData['server_selection']?['trailbase_only'] ?? false,

        // 报告配置
        generateJsonReport:
            jsonData['report_generation']?['generate_json_report'] ?? false,
        generateCsvReport:
            jsonData['report_generation']?['generate_csv_report'] ?? false,
        generateMdReport:
            jsonData['report_generation']?['generate_md_report'] ?? true,
      );
    } catch (e) {
      print('解析配置文件失败: $e，使用默认配置');
      return const TestConfig();
    }
  }

  /// 从命令行参数创建配置
  factory TestConfig.fromArgs(dynamic args) {
    return TestConfig(
      // 阶段控制
      enableInsertTest: _parseBool(args['enable-insert'], true),
      enableQueryTest: _parseBool(args['enable-query'], false),
      enableConnectionTest: _parseBool(args['enable-connection'], false),

      // 插入测试类型控制
      enableSingleInsert: _parseBool(args['enable-single-insert'], true),
      enableBatchInsert: _parseBool(args['enable-batch-insert'], true),
      enableConcurrentInsert: _parseBool(
        args['enable-concurrent-insert'],
        true,
      ),

      // 插入测试配置
      totalRecords: _parseInt(args['records'], 100000),
      batchSize: _parseInt(args['batch-size'], 2000),
      concurrentLevels: _parseIntList(args['concurrency'], [1, 2, 4, 8]),
      intervalSeconds: _parseInt(args['interval'], 10),

      // 查询测试配置
      queryIterations: _parseInt(args['query-iterations'], 1000),
      queryBatchSize: _parseInt(args['query-batch-size'], 100),
      enableIdQuery: _parseBool(args['enable-id-query'], true),
      enableConditionQuery: _parseBool(args['enable-condition-query'], true),
      enableRangeQuery: _parseBool(args['enable-range-query'], true),
      enablePaginationQuery: _parseBool(args['enable-pagination-query'], true),
      enableAggregationQuery: _parseBool(
        args['enable-aggregation-query'],
        true,
      ),
      queryConcurrencyLevels: _parseIntList(args['query-concurrency'], [
        5,
        10,
        20,
      ]),

      // 服务器选择配置
      pocketbaseOnly: _parseBool(args['pocketbase-only'], false),
      trailbaseOnly: _parseBool(args['trailbase-only'], false),

      // 报告配置
      generateJsonReport: _parseBool(args['generate-json'], false),
      generateCsvReport: _parseBool(args['generate-csv'], false),
      generateMdReport: _parseBool(args['generate-md'], true),
    );
  }

  /// 解析布尔值
  static bool _parseBool(dynamic value, bool defaultValue) {
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    return defaultValue;
  }

  /// 解析整数
  static int _parseInt(dynamic value, int defaultValue) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  /// 解析整数列表
  static List<int> _parseIntList(dynamic value, List<int> defaultValue) {
    if (value == null) return defaultValue;
    if (value is String) {
      try {
        return value.split(',').map((s) => int.parse(s.trim())).toList();
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// 从JSON解析整数列表
  static List<int> _parseIntListFromJson(
    dynamic value,
    List<int> defaultValue,
  ) {
    if (value == null) return defaultValue;
    if (value is List) {
      try {
        return value
            .map((e) => e is int ? e : int.parse(e.toString()))
            .toList();
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// 打印配置信息
  void printConfig() {
    print('=== 测试配置 ===');
    print('阶段控制:');
    print('  插入测试: $enableInsertTest');
    print('  查询测试: $enableQueryTest');
    print('  连接测试: $enableConnectionTest');
    print('');

    if (enableInsertTest) {
      print('插入测试配置:');
      print('  启用单条插入: $enableSingleInsert');
      print('  启用批量插入: $enableBatchInsert');
      print('  启用并发插入: $enableConcurrentInsert');
      print('  总记录数: $totalRecords');
      print('  批次大小: $batchSize');
      print('  并发级别: $concurrentLevels');
      print('  测试间隔: ${intervalSeconds}秒');
      print('');
    }

    if (enableQueryTest) {
      print('查询测试配置:');
      print('  查询迭代次数: $queryIterations');
      print('  查询批次大小: $queryBatchSize');
      print('  并发查询级别: $queryConcurrencyLevels');
      print('  启用的查询类型:');
      print('    ID查询: $enableIdQuery');
      print('    条件查询: $enableConditionQuery');
      print('    范围查询: $enableRangeQuery');
      print('    分页查询: $enablePaginationQuery');
      print('    聚合查询: $enableAggregationQuery');
      print('');
    }

    print('服务器选择配置:');
    print('  只测试PocketBase: $pocketbaseOnly');
    print('  只测试TrailBase: $trailbaseOnly');
    print('');

    print('报告配置:');
    print('  生成JSON报告: $generateJsonReport');
    print('  生成CSV报告: $generateCsvReport');
    print('  生成MD报告: $generateMdReport');
    print('');
  }

  /// 验证配置
  bool validate() {
    if (!enableInsertTest && !enableQueryTest && !enableConnectionTest) {
      print('错误: 至少需要启用一个测试阶段');
      return false;
    }

    if (enableInsertTest) {
      if (!enableSingleInsert &&
          !enableBatchInsert &&
          !enableConcurrentInsert) {
        print('错误: 至少需要启用一种插入测试类型');
        return false;
      }
      if (totalRecords <= 0) {
        print('错误: 总记录数必须大于0');
        return false;
      }
      if (batchSize <= 0) {
        print('错误: 批次大小必须大于0');
        return false;
      }
      if (enableConcurrentInsert && concurrentLevels.isEmpty) {
        print('错误: 启用并发插入时，并发级别不能为空');
        return false;
      }
    }

    if (enableQueryTest) {
      if (queryIterations <= 0) {
        print('错误: 查询迭代次数必须大于0');
        return false;
      }
      if (!enableIdQuery &&
          !enableConditionQuery &&
          !enableRangeQuery &&
          !enablePaginationQuery &&
          !enableAggregationQuery) {
        print('错误: 至少需要启用一种查询类型');
        return false;
      }
    }

    if (!generateJsonReport && !generateCsvReport && !generateMdReport) {
      print('错误: 至少需要启用一种报告格式');
      return false;
    }

    return true;
  }
}
