import 'dart:io';
import 'dart:convert';
import '../models/test_record.dart';

class PerformanceTracker {
  final String backend;
  final String testType;
  final List<double> _batchTimes = [];
  final List<String> _errors = [];
  
  int _totalRecords = 0;
  int _successfulInserts = 0;
  int _failedInserts = 0;
  DateTime? _startTime;
  DateTime? _endTime;

  PerformanceTracker({
    required this.backend,
    required this.testType,
  });

  /// 开始测试
  void startTest(int totalRecords) {
    _totalRecords = totalRecords;
    _successfulInserts = 0;
    _failedInserts = 0;
    _batchTimes.clear();
    _errors.clear();
    _startTime = DateTime.now();
    
    print('\n=== 开始 $backend $testType 测试 ===');
    print('目标记录数: $_totalRecords');
    print('开始时间: ${_startTime!.toIso8601String()}');
  }

  /// 记录批次结果
  void recordBatch({
    required int batchSize,
    required int successCount,
    required int failCount,
    required double batchTimeSeconds,
    List<String>? batchErrors,
  }) {
    _successfulInserts += successCount;
    _failedInserts += failCount;
    _batchTimes.add(batchTimeSeconds);

    if (batchErrors != null) {
      _errors.addAll(batchErrors);
    }

    // 计算进度
    final totalProcessed = _successfulInserts + _failedInserts;
    final progress = (totalProcessed / _totalRecords * 100).toStringAsFixed(1);
    final recordsPerSecond = batchSize / batchTimeSeconds;

    print('批次完成: $successCount/$batchSize 成功, '
          '耗时: ${(batchTimeSeconds * 1000).toStringAsFixed(0)}ms, '
          '速度: ${recordsPerSecond.toStringAsFixed(1)} records/s, '
          '总进度: $totalProcessed/$_totalRecords ($progress%)');
  }

  /// 记录单个错误
  void recordError(String error) {
    _errors.add(error);
    _failedInserts++;
  }

  /// 完成测试
  PerformanceResult finishTest() {
    _endTime = DateTime.now();
    
    final totalTimeSeconds = _endTime!.difference(_startTime!).inMilliseconds / 1000.0;
    final averageTimePerRecord = totalTimeSeconds / _totalRecords;
    final recordsPerSecond = _successfulInserts / totalTimeSeconds;

    final result = PerformanceResult(
      backend: backend,
      testType: testType,
      totalRecords: _totalRecords,
      successfulInserts: _successfulInserts,
      failedInserts: _failedInserts,
      totalTimeSeconds: totalTimeSeconds,
      averageTimePerRecord: averageTimePerRecord,
      recordsPerSecond: recordsPerSecond,
      batchTimes: List.from(_batchTimes),
      errors: List.from(_errors),
      timestamp: _startTime!,
    );

    print('\n=== $backend $testType 测试完成 ===');
    print('结束时间: ${_endTime!.toIso8601String()}');
    print(result.toString());

    return result;
  }

  /// 获取当前进度
  double get progress {
    if (_totalRecords == 0) return 0.0;
    return (_successfulInserts + _failedInserts) / _totalRecords;
  }

  /// 获取当前成功率
  double get currentSuccessRate {
    final total = _successfulInserts + _failedInserts;
    if (total == 0) return 0.0;
    return (_successfulInserts / total) * 100;
  }

  /// 获取当前平均速度
  double get currentAverageSpeed {
    if (_batchTimes.isEmpty) return 0.0;
    final totalBatchTime = _batchTimes.reduce((a, b) => a + b);
    return _successfulInserts / totalBatchTime;
  }
}

class ReportGenerator {
  /// 生成JSON报告
  static Future<void> generateJsonReport(
    List<PerformanceResult> results,
    String filename,
  ) async {
    final reportData = {
      'test_session': {
        'timestamp': DateTime.now().toIso8601String(),
        'total_tests': results.length,
      },
      'results': results.map((r) => r.toJson()).toList(),
      'summary': _generateSummary(results),
    };

    final file = File(filename);
    await file.writeAsString(
      const JsonEncoder.withIndent('  ').convert(reportData),
    );

    print('✓ JSON报告已保存: $filename');
  }

  /// 生成CSV报告
  static Future<void> generateCsvReport(
    List<PerformanceResult> results,
    String filename,
  ) async {
    final buffer = StringBuffer();
    
    // CSV头部
    buffer.writeln('Backend,TestType,TotalRecords,SuccessfulInserts,FailedInserts,'
                  'TotalTimeSeconds,RecordsPerSecond,SuccessRate,AverageTimePerRecord');

    // 数据行
    for (final result in results) {
      buffer.writeln('${result.backend},${result.testType},${result.totalRecords},'
                    '${result.successfulInserts},${result.failedInserts},'
                    '${result.totalTimeSeconds.toStringAsFixed(3)},'
                    '${result.recordsPerSecond.toStringAsFixed(2)},'
                    '${result.successRate.toStringAsFixed(2)},'
                    '${(result.averageTimePerRecord * 1000).toStringAsFixed(3)}');
    }

    final file = File(filename);
    await file.writeAsString(buffer.toString());

    print('✓ CSV报告已保存: $filename');
  }

  /// 生成Markdown报告
  static Future<void> generateMarkdownReport(
    List<PerformanceResult> results,
    String filename,
  ) async {
    final buffer = StringBuffer();
    
    buffer.writeln('# 后端性能测试报告');
    buffer.writeln();
    buffer.writeln('**测试时间**: ${DateTime.now().toIso8601String()}');
    buffer.writeln('**测试项目数**: ${results.length}');
    buffer.writeln();

    // 按后端分组
    final groupedResults = <String, List<PerformanceResult>>{};
    for (final result in results) {
      groupedResults.putIfAbsent(result.backend, () => []).add(result);
    }

    for (final entry in groupedResults.entries) {
      buffer.writeln('## ${entry.key} 测试结果');
      buffer.writeln();
      
      buffer.writeln('| 测试类型 | 总记录数 | 成功插入 | 失败数 | 总耗时(s) | 记录/秒 | 成功率(%) |');
      buffer.writeln('|---------|---------|---------|--------|----------|---------|----------|');
      
      for (final result in entry.value) {
        buffer.writeln('| ${result.testType} | ${result.totalRecords} | '
                      '${result.successfulInserts} | ${result.failedInserts} | '
                      '${result.totalTimeSeconds.toStringAsFixed(2)} | '
                      '${result.recordsPerSecond.toStringAsFixed(2)} | '
                      '${result.successRate.toStringAsFixed(1)} |');
      }
      buffer.writeln();
    }

    // 添加总结
    final summary = _generateSummary(results);
    buffer.writeln('## 测试总结');
    buffer.writeln();
    buffer.writeln('- **最快插入速度**: ${summary['fastest_backend']} '
                  '(${summary['max_records_per_second'].toStringAsFixed(2)} records/s)');
    buffer.writeln('- **最高成功率**: ${summary['most_reliable_backend']} '
                  '(${summary['max_success_rate'].toStringAsFixed(1)}%)');
    buffer.writeln('- **平均插入速度**: ${summary['average_records_per_second'].toStringAsFixed(2)} records/s');

    final file = File(filename);
    await file.writeAsString(buffer.toString());

    print('✓ Markdown报告已保存: $filename');
  }

  /// 生成测试总结
  static Map<String, dynamic> _generateSummary(List<PerformanceResult> results) {
    if (results.isEmpty) return {};

    final maxRecordsPerSecond = results.map((r) => r.recordsPerSecond).reduce((a, b) => a > b ? a : b);
    final maxSuccessRate = results.map((r) => r.successRate).reduce((a, b) => a > b ? a : b);
    final averageRecordsPerSecond = results.map((r) => r.recordsPerSecond).reduce((a, b) => a + b) / results.length;

    final fastestResult = results.firstWhere((r) => r.recordsPerSecond == maxRecordsPerSecond);
    final mostReliableResult = results.firstWhere((r) => r.successRate == maxSuccessRate);

    return {
      'max_records_per_second': maxRecordsPerSecond,
      'fastest_backend': '${fastestResult.backend} (${fastestResult.testType})',
      'max_success_rate': maxSuccessRate,
      'most_reliable_backend': '${mostReliableResult.backend} (${mostReliableResult.testType})',
      'average_records_per_second': averageRecordsPerSecond,
      'total_tests': results.length,
    };
  }
}
