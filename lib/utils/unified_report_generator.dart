import 'dart:io';
import '../models/test_record.dart';
import '../models/connection_test_result.dart';
import '../tests/query_performance_test.dart';
import '../config/test_config.dart';

/// 统一的测试报告生成器
class UnifiedReportGenerator {
  static Future<void> generateUnifiedReport({
    required List<PerformanceResult> insertResults,
    required Map<String, QueryTestResult> queryResults,
    List<ConnectionTestResult>? connectionResults,
    required TestConfig config,
  }) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filename = 'performance_test_results_$timestamp.md';
    
    final buffer = StringBuffer();
    
    // 报告标题和基本信息
    buffer.writeln('# 后端性能测试对比报告');
    buffer.writeln();
    buffer.writeln('**测试时间**: ${DateTime.now().toIso8601String()}');
    buffer.writeln();
    
    // 测试配置
    _writeTestConfig(buffer, config);
    
    // 插入性能测试结果
    if (insertResults.isNotEmpty) {
      _writeInsertResults(buffer, insertResults);
    }
    
    // 查询性能测试结果
    if (queryResults.isNotEmpty) {
      _writeQueryResults(buffer, queryResults, config);
    }

    // 连接测试结果
    if (connectionResults != null && connectionResults.isNotEmpty) {
      _writeConnectionResults(buffer, connectionResults);
    }

    // 综合性能总结
    _writeOverallSummary(buffer, insertResults, queryResults, connectionResults);
    
    // 写入文件
    final file = File(filename);
    await file.writeAsString(buffer.toString());
    
    print('📄 统一测试报告已生成: $filename');
  }
  
  /// 写入测试配置信息
  static void _writeTestConfig(StringBuffer buffer, TestConfig config) {
    buffer.writeln('## 测试配置');
    buffer.writeln();
    
    if (config.enableInsertTest) {
      buffer.writeln('### 插入测试配置');
      buffer.writeln('- 总记录数: ${config.totalRecords}');
      buffer.writeln('- 批次大小: ${config.batchSize}');
      buffer.writeln('- 并发级别: ${config.concurrentLevels}');
      buffer.writeln();
    }
    
    if (config.enableQueryTest) {
      buffer.writeln('### 查询测试配置');
      buffer.writeln('- 查询迭代次数: ${config.queryIterations}');
      buffer.writeln('- 查询批次大小: ${config.queryBatchSize}');
      buffer.writeln();
    }
  }
  
  /// 写入插入性能测试结果
  static void _writeInsertResults(StringBuffer buffer, List<PerformanceResult> results) {
    buffer.writeln('## 插入性能测试结果');
    buffer.writeln();
    
    // 按后端分组
    final groupedResults = <String, List<PerformanceResult>>{};
    for (final result in results) {
      groupedResults.putIfAbsent(result.backend, () => []).add(result);
    }
    
    // 创建对比表格
    final testTypes = <String>[];
    final pocketbaseResults = <String, PerformanceResult>{};
    final trailbaseResults = <String, PerformanceResult>{};
    
    for (final entry in groupedResults.entries) {
      for (final result in entry.value) {
        if (!testTypes.contains(result.testType)) {
          testTypes.add(result.testType);
        }
        
        if (entry.key == 'PocketBase') {
          pocketbaseResults[result.testType] = result;
        } else if (entry.key == 'TrailBase') {
          trailbaseResults[result.testType] = result;
        }
      }
    }
    
    // 插入性能对比表格
    buffer.writeln('### 插入性能对比');
    buffer.writeln();
    buffer.writeln('| 测试类型 | PocketBase (records/s) | TrailBase (records/s) | 性能对比 |');
    buffer.writeln('|---------|----------------------|---------------------|----------|');
    
    for (final testType in testTypes) {
      final pbResult = pocketbaseResults[testType];
      final tbResult = trailbaseResults[testType];
      
      final pbSpeed = pbResult?.recordsPerSecond.toStringAsFixed(2) ?? '-';
      final tbSpeed = tbResult?.recordsPerSecond.toStringAsFixed(2) ?? '-';
      
      String comparison = '-';
      if (pbResult != null && tbResult != null) {
        final advantage = (tbResult.recordsPerSecond / pbResult.recordsPerSecond - 1) * 100;
        comparison = advantage > 0 
            ? 'TrailBase +${advantage.toStringAsFixed(1)}%'
            : 'PocketBase +${(-advantage).toStringAsFixed(1)}%';
      } else if (pbResult != null && tbResult == null) {
        comparison = 'PocketBase 独有';
      } else if (pbResult == null && tbResult != null) {
        comparison = 'TrailBase 独有';
      }
      
      buffer.writeln('| $testType | $pbSpeed | $tbSpeed | $comparison |');
    }
    buffer.writeln();
    
    // 详细结果
    _writeDetailedInsertResults(buffer, groupedResults);
  }
  
  /// 写入详细插入测试结果
  static void _writeDetailedInsertResults(StringBuffer buffer, Map<String, List<PerformanceResult>> groupedResults) {
    buffer.writeln('### 详细插入测试结果');
    buffer.writeln();
    
    for (final entry in groupedResults.entries) {
      buffer.writeln('#### ${entry.key}');
      buffer.writeln();
      buffer.writeln('| 测试类型 | 总记录数 | 成功插入 | 失败数 | 总耗时(s) | 记录/秒 | 成功率(%) |');
      buffer.writeln('|---------|---------|---------|--------|----------|---------|----------|');
      
      for (final result in entry.value) {
        buffer.writeln('| ${result.testType} | ${result.totalRecords} | '
                      '${result.successfulInserts} | ${result.failedInserts} | '
                      '${result.totalTimeSeconds.toStringAsFixed(2)} | '
                      '${result.recordsPerSecond.toStringAsFixed(2)} | '
                      '${result.successRate.toStringAsFixed(1)} |');
      }
      buffer.writeln();
    }
  }
  
  /// 写入查询性能测试结果
  static void _writeQueryResults(StringBuffer buffer, Map<String, QueryTestResult> results, TestConfig config) {
    buffer.writeln('## 查询性能测试结果');
    buffer.writeln();
    
    // 提取查询类型和后端
    final queryTypes = ['id_query', 'condition_query', 'range_query', 'pagination_query', 'aggregation_query'];
    final queryNames = ['ID查询', '条件查询', '范围查询', '分页查询', '聚合查询'];

    // 动态提取并发查询类型
    final concurrentQueryTypes = <String>[];
    final concurrentQueryNames = <String>[];
    for (final key in results.keys) {
      if (key.contains('concurrent_query_') && key.contains('threads')) {
        final parts = key.split('_');
        if (parts.length >= 4) {
          final threads = parts[3]; // 例如: "5threads"
          final threadNum = threads.replaceAll('threads', '');
          final queryType = 'concurrent_query_${threads}';
          if (!concurrentQueryTypes.contains(queryType)) {
            concurrentQueryTypes.add(queryType);
            concurrentQueryNames.add('${threadNum}线程并发');
          }
        }
      }
    }

    // 合并所有查询类型
    final allQueryTypes = [...queryTypes, ...concurrentQueryTypes];
    final allQueryNames = [...queryNames, ...concurrentQueryNames];
    
    final pocketbaseResults = <String, QueryTestResult>{};
    final trailbaseResults = <String, QueryTestResult>{};
    
    for (final entry in results.entries) {
      if (entry.key.startsWith('PocketBase_')) {
        final queryType = entry.key.substring('PocketBase_'.length);
        pocketbaseResults[queryType] = entry.value;
      } else if (entry.key.startsWith('TrailBase_')) {
        final queryType = entry.key.substring('TrailBase_'.length);
        trailbaseResults[queryType] = entry.value;
      }
    }
    
    // 查询性能对比表格
    buffer.writeln('### 查询性能对比');
    buffer.writeln();
    buffer.writeln('| 查询类型 | PocketBase (queries/s) | TrailBase (queries/s) | 性能对比 |');
    buffer.writeln('|---------|----------------------|---------------------|----------|');
    
    for (int i = 0; i < allQueryTypes.length; i++) {
      final queryType = allQueryTypes[i];
      final queryName = allQueryNames[i];
      
      final pbResult = pocketbaseResults[queryType];
      final tbResult = trailbaseResults[queryType];
      
      final pbSpeed = pbResult?.queriesPerSecond.toStringAsFixed(2) ?? '-';
      final tbSpeed = tbResult?.queriesPerSecond.toStringAsFixed(2) ?? '-';
      
      String comparison = '-';
      if (pbResult != null && tbResult != null) {
        final advantage = (tbResult.queriesPerSecond / pbResult.queriesPerSecond - 1) * 100;
        comparison = advantage > 0 
            ? 'TrailBase +${advantage.toStringAsFixed(1)}%'
            : 'PocketBase +${(-advantage).toStringAsFixed(1)}%';
      } else if (pbResult != null && tbResult == null) {
        comparison = 'PocketBase 独有';
      } else if (pbResult == null && tbResult != null) {
        comparison = 'TrailBase 独有';
      }
      
      buffer.writeln('| $queryName | $pbSpeed | $tbSpeed | $comparison |');
    }
    buffer.writeln();
    
    // 详细查询结果
    _writeDetailedQueryResults(buffer, results);
  }
  
  /// 写入详细查询测试结果
  static void _writeDetailedQueryResults(StringBuffer buffer, Map<String, QueryTestResult> results) {
    buffer.writeln('### 详细查询测试结果');
    buffer.writeln();
    
    final backends = <String>[];
    for (final key in results.keys) {
      final backend = key.split('_')[0];
      if (!backends.contains(backend)) {
        backends.add(backend);
      }
    }
    
    for (final backend in backends) {
      buffer.writeln('#### $backend');
      buffer.writeln();
      buffer.writeln('| 查询类型 | 总耗时(ms) | 成功查询 | 总查询数 | 查询速度(queries/s) | 成功率(%) |');
      buffer.writeln('|---------|-----------|---------|---------|-------------------|----------|');
      
      for (final entry in results.entries) {
        if (entry.key.startsWith('${backend}_')) {
          final result = entry.value;
          final queryType = entry.key.substring('${backend}_'.length);
          final successRate = result.totalQueries > 0 
              ? (result.successfulQueries / result.totalQueries * 100).toStringAsFixed(1)
              : '0.0';
          
          buffer.writeln('| $queryType | ${result.duration.inMilliseconds} | '
                        '${result.successfulQueries} | ${result.totalQueries} | '
                        '${result.queriesPerSecond.toStringAsFixed(2)} | $successRate |');
        }
      }
      buffer.writeln();
    }
  }
  
  /// 写入连接测试结果
  static void _writeConnectionResults(StringBuffer buffer, List<ConnectionTestResult> results) {
    buffer.writeln('## 连接测试结果');
    buffer.writeln();

    // 按后端分组
    final groupedResults = <String, List<ConnectionTestResult>>{};
    for (final result in results) {
      groupedResults.putIfAbsent(result.backendName, () => []).add(result);
    }

    // 连接性能对比表格
    buffer.writeln('### 连接性能对比');
    buffer.writeln();
    buffer.writeln('| 连接数 | PocketBase (成功率) | TrailBase (成功率) | 性能对比 |');
    buffer.writeln('|--------|-------------------|------------------|----------|');

    final pocketbaseResults = groupedResults['PocketBase'] ?? [];
    final trailbaseResults = groupedResults['TrailBase'] ?? [];

    // 找到所有测试的连接数级别
    final allLevels = <int>{};
    for (final result in results) {
      allLevels.add(result.targetConnections);
    }
    final sortedLevels = allLevels.toList()..sort();

    for (final level in sortedLevels) {
      final pbResult = pocketbaseResults.where((r) => r.targetConnections == level).firstOrNull;
      final tbResult = trailbaseResults.where((r) => r.targetConnections == level).firstOrNull;

      String pbText = pbResult != null ?
        '${pbResult.successfulConnections} (${pbResult.connectionSuccessRate.toStringAsFixed(1)}%)' :
        'N/A';
      String tbText = tbResult != null ?
        '${tbResult.successfulConnections} (${tbResult.connectionSuccessRate.toStringAsFixed(1)}%)' :
        'N/A';

      String comparison = 'N/A';
      if (pbResult != null && tbResult != null) {
        if (tbResult.successfulConnections > pbResult.successfulConnections) {
          final improvement = ((tbResult.successfulConnections - pbResult.successfulConnections) / pbResult.successfulConnections * 100);
          comparison = 'TrailBase +${improvement.toStringAsFixed(1)}%';
        } else if (pbResult.successfulConnections > tbResult.successfulConnections) {
          final improvement = ((pbResult.successfulConnections - tbResult.successfulConnections) / tbResult.successfulConnections * 100);
          comparison = 'PocketBase +${improvement.toStringAsFixed(1)}%';
        } else {
          comparison = '相同';
        }
      }

      buffer.writeln('| $level | $pbText | $tbText | $comparison |');
    }
    buffer.writeln();

    // 详细连接测试结果
    for (final entry in groupedResults.entries) {
      buffer.writeln('### ${entry.key}');
      buffer.writeln();
      buffer.writeln('| 连接数 | 成功连接 | 成功率(%) | 稳定性(%) | 平均延迟(ms) | 状态 |');
      buffer.writeln('|--------|---------|----------|----------|-------------|------|');

      for (final result in entry.value) {
        buffer.writeln('| ${result.targetConnections} | ${result.successfulConnections} | '
                      '${result.connectionSuccessRate.toStringAsFixed(1)} | '
                      '${result.connectionStabilityRate.toStringAsFixed(1)} | '
                      '${result.averageMessageLatency.toStringAsFixed(0)} | '
                      '${result.statusDescription} |');
      }
      buffer.writeln();
    }
  }

  /// 写入综合性能总结
  static void _writeOverallSummary(StringBuffer buffer, List<PerformanceResult> insertResults, Map<String, QueryTestResult> queryResults, List<ConnectionTestResult>? connectionResults) {
    buffer.writeln('## 综合性能总结');
    buffer.writeln();
    
    if (insertResults.isNotEmpty) {
      // 插入性能总结
      final bestInsertResult = insertResults.reduce((a, b) => a.recordsPerSecond > b.recordsPerSecond ? a : b);
      buffer.writeln('### 插入性能');
      buffer.writeln('- **最佳插入性能**: ${bestInsertResult.backend} (${bestInsertResult.testType})');
      buffer.writeln('  - 速度: ${bestInsertResult.recordsPerSecond.toStringAsFixed(2)} records/s');
      buffer.writeln('  - 成功率: ${bestInsertResult.successRate.toStringAsFixed(1)}%');
      buffer.writeln();
    }
    
    if (queryResults.isNotEmpty) {
      // 查询性能总结
      final bestQueryResult = queryResults.values.reduce((a, b) => a.queriesPerSecond > b.queriesPerSecond ? a : b);
      buffer.writeln('### 查询性能');
      buffer.writeln('- **最佳查询性能**: ${bestQueryResult.testName}');
      buffer.writeln('  - 速度: ${bestQueryResult.queriesPerSecond.toStringAsFixed(2)} queries/s');
      final successRate = bestQueryResult.totalQueries > 0
          ? (bestQueryResult.successfulQueries / bestQueryResult.totalQueries * 100).toStringAsFixed(1)
          : '0.0';
      buffer.writeln('  - 成功率: $successRate%');
      buffer.writeln();
    }

    if (connectionResults != null && connectionResults.isNotEmpty) {
      // 连接性能总结
      final bestConnectionResult = connectionResults.reduce((a, b) => a.successfulConnections > b.successfulConnections ? a : b);
      buffer.writeln('### 连接性能');
      buffer.writeln('- **最大稳定连接数**: ${bestConnectionResult.backendName}');
      buffer.writeln('  - 连接数: ${bestConnectionResult.successfulConnections}');
      buffer.writeln('  - 成功率: ${bestConnectionResult.connectionSuccessRate.toStringAsFixed(1)}%');
      buffer.writeln('  - 稳定性: ${bestConnectionResult.connectionStabilityRate.toStringAsFixed(1)}%');
      buffer.writeln('  - 平均延迟: ${bestConnectionResult.averageMessageLatency.toStringAsFixed(0)}ms');
      buffer.writeln();
    }

    // 推荐建议
    _writeRecommendations(buffer, insertResults, queryResults, connectionResults);
  }
  
  /// 写入推荐建议
  static void _writeRecommendations(StringBuffer buffer, List<PerformanceResult> insertResults, Map<String, QueryTestResult> queryResults, List<ConnectionTestResult>? connectionResults) {
    buffer.writeln('## 推荐建议');
    buffer.writeln();
    
    // 基于测试结果给出建议
    final hasTrailBase = insertResults.any((r) => r.backend == 'TrailBase') || 
                        queryResults.keys.any((k) => k.startsWith('TrailBase_'));
    final hasPocketBase = insertResults.any((r) => r.backend == 'PocketBase') || 
                         queryResults.keys.any((k) => k.startsWith('PocketBase_'));
    
    if (hasTrailBase && hasPocketBase) {
      buffer.writeln('### 选择TrailBase的场景');
      buffer.writeln('- 需要高性能数据插入和查询的应用');
      buffer.writeln('- 对并发写入有较高要求的系统');
      buffer.writeln('- 表结构相对固定的项目');
      buffer.writeln();
      
      buffer.writeln('### 选择PocketBase的场景');
      buffer.writeln('- 需要快速原型开发和部署');
      buffer.writeln('- 表结构经常变化的项目');
      buffer.writeln('- 需要完整后端功能和管理界面的应用');
      buffer.writeln();
    } else if (hasTrailBase) {
      buffer.writeln('- TrailBase表现出色，适合高性能要求的应用场景');
      buffer.writeln();
    } else if (hasPocketBase) {
      buffer.writeln('- PocketBase提供了稳定可靠的性能，适合快速开发和部署');
      buffer.writeln();
    }
  }
}
