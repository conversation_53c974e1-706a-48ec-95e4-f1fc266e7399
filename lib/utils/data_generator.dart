import 'dart:math';
import '../models/test_record.dart';

class DataGenerator {
  static final Random _random = Random();

  static final List<String> _firstNames = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  static final List<String> _lastNames = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  static final List<String> _domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'example.com',
    'test.com',
    'demo.org',
    'sample.net',
    'mock.io',
    'fake.co',
  ];

  static final List<String> _descriptions = [
    'A passionate software developer with expertise in mobile applications.',
    'Experienced project manager with a track record of successful deliveries.',
    'Creative designer focused on user experience and interface design.',
    'Data analyst with strong skills in statistical analysis and visualization.',
    'Marketing specialist with deep understanding of digital campaigns.',
    'Sales professional with excellent communication and negotiation skills.',
    'Operations manager ensuring smooth business processes and efficiency.',
    'Quality assurance engineer dedicated to delivering bug-free software.',
    'DevOps engineer specializing in cloud infrastructure and automation.',
    'Product owner with strong business acumen and technical knowledge.',
    'Full-stack developer comfortable with both frontend and backend technologies.',
    'Machine learning engineer working on cutting-edge AI solutions.',
    'Cybersecurity expert protecting organizations from digital threats.',
    'Database administrator ensuring data integrity and optimal performance.',
    'Technical writer creating clear documentation for complex systems.',
  ];

  /// 生成单条测试记录
  static TestRecord generateRecord() {
    final firstName = _firstNames[_random.nextInt(_firstNames.length)];
    final lastName = _lastNames[_random.nextInt(_lastNames.length)];
    final domain = _domains[_random.nextInt(_domains.length)];

    return TestRecord(
      name: '$firstName $lastName',
      email: '${firstName.toLowerCase()}.${lastName.toLowerCase()}@$domain',
      age: 18 + _random.nextInt(63), // 18-80岁
      score: _random.nextDouble() * 100, // 0-100分
      description: _descriptions[_random.nextInt(_descriptions.length)],
    );
  }

  /// 批量生成测试记录（精简输出模式）
  static List<TestRecord> generateRecords(int count) {
    // 只在生成大量数据时打印开始信息
    if (count >= 1000) {
      print('🔄 生成 $count 条测试记录 (用于数据库初始化)...');
    }

    final records = <TestRecord>[];
    final startTime = DateTime.now();

    for (int i = 0; i < count; i++) {
      records.add(generateRecord());

      // 每生成10000条数据显示进度
      if (count >= 1000 && (i + 1) % 10000 == 0) {
        final elapsed = DateTime.now().difference(startTime).inMilliseconds;
        final progress = ((i + 1) / count * 100).toStringAsFixed(1);
        print('  生成进度: $progress% (${i + 1}/$count) - 耗时: ${elapsed}ms');
      }
    }

    final totalTime = DateTime.now().difference(startTime).inMilliseconds;
    // 只在生成大量数据时打印完成信息
    if (count >= 1000) {
      print('✅ 测试记录生成完成: $count 条记录，耗时: ${totalTime}ms');
    }

    return records;
  }

  /// 将记录列表分批
  static List<List<TestRecord>> batchRecords(
    List<TestRecord> records,
    int batchSize,
  ) {
    final batches = <List<TestRecord>>[];

    for (int i = 0; i < records.length; i += batchSize) {
      final end = (i + batchSize < records.length)
          ? i + batchSize
          : records.length;
      batches.add(records.sublist(i, end));
    }

    print(
      '数据分批完成: ${records.length} 条记录分为 ${batches.length} 批，每批最多 $batchSize 条',
    );
    return batches;
  }

  /// 生成随机字符串
  static String generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(_random.nextInt(chars.length)),
      ),
    );
  }

  /// 生成测试用的表名
  static String generateTableName() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'performance_test_$timestamp';
  }
}
