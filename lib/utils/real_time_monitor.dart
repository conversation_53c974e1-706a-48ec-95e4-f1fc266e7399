import 'dart:async';
import 'dart:collection';
import 'dart:math';
import 'progressive_load_controller.dart';

/// 请求结果
class RequestResult {
  final bool success;
  final int responseTimeMs;
  final String? error;
  final DateTime timestamp;
  final String operation;

  RequestResult({
    required this.success,
    required this.responseTimeMs,
    this.error,
    required this.timestamp,
    required this.operation,
  });
}

/// 实时性能监控器
class RealTimeMonitor {
  final int metricsCollectionIntervalSeconds;
  final int performanceCheckIntervalSeconds;
  final double successRateThreshold;
  final double responseTimeThresholdMs;
  final int maxHistorySize;

  // 请求结果队列
  final Queue<RequestResult> _requestResults = Queue<RequestResult>();
  
  // 定时器
  Timer? _metricsTimer;
  Timer? _performanceCheckTimer;
  
  // 统计数据
  int _totalRequests = 0;
  int _successfulRequests = 0;
  int _failedRequests = 0;
  int _timeoutRequests = 0;
  
  // 响应时间统计
  final List<int> _responseTimes = [];
  
  // 回调函数
  Function(PerformanceMetrics metrics)? onMetricsCollected;
  Function(String alert)? onPerformanceAlert;
  Function(Map<String, dynamic> stats)? onStatsUpdated;

  RealTimeMonitor({
    required this.metricsCollectionIntervalSeconds,
    required this.performanceCheckIntervalSeconds,
    required this.successRateThreshold,
    required this.responseTimeThresholdMs,
    this.maxHistorySize = 10000,
  });

  /// 开始监控
  void startMonitoring() {
    print('📊 开始实时性能监控');
    print('   指标收集间隔: ${metricsCollectionIntervalSeconds}秒');
    print('   性能检查间隔: ${performanceCheckIntervalSeconds}秒');
    
    // 启动指标收集定时器
    _metricsTimer = Timer.periodic(
      Duration(seconds: metricsCollectionIntervalSeconds),
      (timer) => _collectMetrics(),
    );
    
    // 启动性能检查定时器
    _performanceCheckTimer = Timer.periodic(
      Duration(seconds: performanceCheckIntervalSeconds),
      (timer) => _performPerformanceCheck(),
    );
  }

  /// 停止监控
  void stopMonitoring() {
    _metricsTimer?.cancel();
    _performanceCheckTimer?.cancel();
    _metricsTimer = null;
    _performanceCheckTimer = null;
    print('⏹️ 实时性能监控已停止');
  }

  /// 记录请求结果
  void recordRequest(RequestResult result) {
    _requestResults.add(result);
    
    // 更新统计
    _totalRequests++;
    if (result.success) {
      _successfulRequests++;
    } else {
      _failedRequests++;
      if (result.error?.contains('timeout') == true) {
        _timeoutRequests++;
      }
    }
    
    // 记录响应时间
    _responseTimes.add(result.responseTimeMs);
    
    // 限制历史记录大小
    if (_requestResults.length > maxHistorySize) {
      final removed = _requestResults.removeFirst();
      if (removed.success) {
        _successfulRequests--;
      } else {
        _failedRequests--;
        if (removed.error?.contains('timeout') == true) {
          _timeoutRequests--;
        }
      }
      _totalRequests--;
    }
    
    // 限制响应时间列表大小
    if (_responseTimes.length > maxHistorySize) {
      _responseTimes.removeAt(0);
    }
  }

  /// 收集性能指标
  void _collectMetrics() {
    if (_totalRequests == 0) return;
    
    final successRate = _successfulRequests / _totalRequests;
    final averageResponseTime = _responseTimes.isEmpty 
        ? 0.0 
        : _responseTimes.reduce((a, b) => a + b) / _responseTimes.length;
    
    final p95ResponseTime = _calculatePercentile(_responseTimes, 0.95);
    
    final metrics = PerformanceMetrics(
      currentUsers: 0, // 这个值会由外部设置
      successRate: successRate,
      averageResponseTime: averageResponseTime,
      p95ResponseTime: p95ResponseTime,
      timeoutCount: _timeoutRequests,
      errorCount: _failedRequests,
      totalRequests: _totalRequests,
      timestamp: DateTime.now(),
    );
    
    onMetricsCollected?.call(metrics);
    
    // 更新统计信息
    _updateStats();
  }

  /// 执行性能检查
  void _performPerformanceCheck() {
    if (_totalRequests == 0) return;
    
    final successRate = _successfulRequests / _totalRequests;
    final averageResponseTime = _responseTimes.isEmpty 
        ? 0.0 
        : _responseTimes.reduce((a, b) => a + b) / _responseTimes.length;
    
    // 检查成功率
    if (successRate < successRateThreshold) {
      final alert = '⚠️ 成功率过低: ${(successRate * 100).toStringAsFixed(1)}% '
                   '(阈值: ${(successRateThreshold * 100).toStringAsFixed(1)}%)';
      onPerformanceAlert?.call(alert);
    }
    
    // 检查响应时间
    if (averageResponseTime > responseTimeThresholdMs) {
      final alert = '⚠️ 响应时间过长: ${averageResponseTime.toStringAsFixed(0)}ms '
                   '(阈值: ${responseTimeThresholdMs.toStringAsFixed(0)}ms)';
      onPerformanceAlert?.call(alert);
    }
    
    // 检查超时率
    final timeoutRate = _totalRequests > 0 ? _timeoutRequests / _totalRequests : 0.0;
    if (timeoutRate > 0.1) { // 超时率超过10%
      final alert = '⚠️ 超时率过高: ${(timeoutRate * 100).toStringAsFixed(1)}%';
      onPerformanceAlert?.call(alert);
    }
  }

  /// 更新统计信息
  void _updateStats() {
    final stats = getCurrentStats();
    onStatsUpdated?.call(stats);
  }

  /// 获取当前统计信息
  Map<String, dynamic> getCurrentStats() {
    if (_totalRequests == 0) {
      return {
        'total_requests': 0,
        'success_rate': 0.0,
        'error_rate': 0.0,
        'timeout_rate': 0.0,
        'average_response_time': 0.0,
        'min_response_time': 0,
        'max_response_time': 0,
        'p50_response_time': 0.0,
        'p95_response_time': 0.0,
        'p99_response_time': 0.0,
        'requests_per_second': 0.0,
      };
    }

    final successRate = _successfulRequests / _totalRequests;
    final errorRate = _failedRequests / _totalRequests;
    final timeoutRate = _timeoutRequests / _totalRequests;
    
    final averageResponseTime = _responseTimes.isEmpty 
        ? 0.0 
        : _responseTimes.reduce((a, b) => a + b) / _responseTimes.length;
    
    final minResponseTime = _responseTimes.isEmpty ? 0 : _responseTimes.reduce(min);
    final maxResponseTime = _responseTimes.isEmpty ? 0 : _responseTimes.reduce(max);
    
    final p50ResponseTime = _calculatePercentile(_responseTimes, 0.5);
    final p95ResponseTime = _calculatePercentile(_responseTimes, 0.95);
    final p99ResponseTime = _calculatePercentile(_responseTimes, 0.99);
    
    // 计算每秒请求数（基于最近的请求）
    final now = DateTime.now();
    final recentRequests = _requestResults
        .where((r) => now.difference(r.timestamp).inSeconds <= 60)
        .length;
    final requestsPerSecond = recentRequests / 60.0;

    return {
      'total_requests': _totalRequests,
      'successful_requests': _successfulRequests,
      'failed_requests': _failedRequests,
      'timeout_requests': _timeoutRequests,
      'success_rate': successRate,
      'error_rate': errorRate,
      'timeout_rate': timeoutRate,
      'average_response_time': averageResponseTime,
      'min_response_time': minResponseTime,
      'max_response_time': maxResponseTime,
      'p50_response_time': p50ResponseTime,
      'p95_response_time': p95ResponseTime,
      'p99_response_time': p99ResponseTime,
      'requests_per_second': requestsPerSecond,
      'timestamp': now.toIso8601String(),
    };
  }

  /// 计算百分位数
  double _calculatePercentile(List<int> values, double percentile) {
    if (values.isEmpty) return 0.0;
    
    final sorted = List<int>.from(values)..sort();
    final index = (sorted.length * percentile).floor();
    
    if (index >= sorted.length) {
      return sorted.last.toDouble();
    }
    
    return sorted[index].toDouble();
  }

  /// 获取操作类型统计
  Map<String, Map<String, dynamic>> getOperationStats() {
    final operationStats = <String, Map<String, dynamic>>{};
    
    for (final result in _requestResults) {
      final operation = result.operation;
      
      if (!operationStats.containsKey(operation)) {
        operationStats[operation] = {
          'total': 0,
          'successful': 0,
          'failed': 0,
          'response_times': <int>[],
        };
      }
      
      final stats = operationStats[operation]!;
      stats['total'] = (stats['total'] as int) + 1;
      
      if (result.success) {
        stats['successful'] = (stats['successful'] as int) + 1;
      } else {
        stats['failed'] = (stats['failed'] as int) + 1;
      }
      
      (stats['response_times'] as List<int>).add(result.responseTimeMs);
    }
    
    // 计算每个操作的统计信息
    for (final entry in operationStats.entries) {
      final stats = entry.value;
      final total = stats['total'] as int;
      final successful = stats['successful'] as int;
      final responseTimes = stats['response_times'] as List<int>;
      
      stats['success_rate'] = total > 0 ? successful / total : 0.0;
      stats['average_response_time'] = responseTimes.isEmpty 
          ? 0.0 
          : responseTimes.reduce((a, b) => a + b) / responseTimes.length;
      stats['p95_response_time'] = _calculatePercentile(responseTimes, 0.95);
      
      // 移除原始响应时间数据以节省内存
      stats.remove('response_times');
    }
    
    return operationStats;
  }

  /// 获取最近的错误信息
  List<Map<String, dynamic>> getRecentErrors({int limit = 10}) {
    final errors = <Map<String, dynamic>>[];
    
    for (final result in _requestResults.toList().reversed) {
      if (!result.success && result.error != null) {
        errors.add({
          'timestamp': result.timestamp.toIso8601String(),
          'operation': result.operation,
          'error': result.error,
          'response_time': result.responseTimeMs,
        });
        
        if (errors.length >= limit) break;
      }
    }
    
    return errors;
  }

  /// 重置统计数据
  void resetStats() {
    _requestResults.clear();
    _responseTimes.clear();
    _totalRequests = 0;
    _successfulRequests = 0;
    _failedRequests = 0;
    _timeoutRequests = 0;
    print('📊 统计数据已重置');
  }

  /// 获取监控摘要
  String getMonitoringSummary() {
    if (_totalRequests == 0) {
      return '暂无请求数据';
    }

    final stats = getCurrentStats();
    final successRate = (stats['success_rate'] as double) * 100;
    final avgResponseTime = stats['average_response_time'] as double;
    final requestsPerSecond = stats['requests_per_second'] as double;

    return '总请求: $_totalRequests, '
           '成功率: ${successRate.toStringAsFixed(1)}%, '
           '平均响应时间: ${avgResponseTime.toStringAsFixed(0)}ms, '
           'RPS: ${requestsPerSecond.toStringAsFixed(1)}';
  }
}
