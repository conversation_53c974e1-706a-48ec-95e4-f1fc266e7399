import 'dart:async';
import 'dart:math';

/// 性能指标数据
class PerformanceMetrics {
  final int currentUsers;
  final double successRate;
  final double averageResponseTime;
  final double p95ResponseTime;
  final int timeoutCount;
  final int errorCount;
  final int totalRequests;
  final DateTime timestamp;

  PerformanceMetrics({
    required this.currentUsers,
    required this.successRate,
    required this.averageResponseTime,
    required this.p95ResponseTime,
    required this.timeoutCount,
    required this.errorCount,
    required this.totalRequests,
    required this.timestamp,
  });

  bool isHealthy(double successThreshold, double responseTimeThreshold) {
    return successRate >= successThreshold &&
        averageResponseTime <= responseTimeThreshold;
  }
}

/// 渐进式负载控制器
class ProgressiveLoadController {
  final int initialUsers;
  final int maxUsers;
  final int baseIncrementStep;
  final int incrementIntervalSeconds;
  final double successRateThreshold;
  final double responseTimeThresholdMs;
  final int failureThresholdConsecutive;

  int _currentUsers;
  int _currentIncrementStep;
  int _consecutiveFailures = 0;
  bool _isStable = true;
  bool _hasReachedLimit = false;
  int _maxStableUsers = 0;

  final List<PerformanceMetrics> _metricsHistory = [];
  Timer? _incrementTimer;

  // 回调函数
  Function(int newUserCount)? onUserCountChanged;
  Function(String reason)? onTestStopped;
  Function(PerformanceMetrics metrics)? onMetricsUpdated;

  ProgressiveLoadController({
    required this.initialUsers,
    required this.maxUsers,
    required this.baseIncrementStep,
    required this.incrementIntervalSeconds,
    required this.successRateThreshold,
    required this.responseTimeThresholdMs,
    required this.failureThresholdConsecutive,
  }) : _currentUsers = initialUsers,
       _currentIncrementStep = baseIncrementStep;

  /// 当前用户数
  int get currentUsers => _currentUsers;

  /// 是否已达到极限
  bool get hasReachedLimit => _hasReachedLimit;

  /// 最大稳定用户数
  int get maxStableUsers => _maxStableUsers;

  /// 性能指标历史
  List<PerformanceMetrics> get metricsHistory =>
      List.unmodifiable(_metricsHistory);

  /// 开始渐进式负载测试
  void startProgressiveLoad() {
    print('🚀 开始渐进式负载测试');
    print('   初始用户数: $initialUsers');
    print('   最大用户数: $maxUsers');
    print('   基础递增步长: $baseIncrementStep');
    print('   递增间隔: ${incrementIntervalSeconds}秒');

    _currentUsers = initialUsers;
    _currentIncrementStep = baseIncrementStep;
    _consecutiveFailures = 0;
    _isStable = true;
    _hasReachedLimit = false;
    _maxStableUsers = initialUsers;

    // 通知初始用户数
    onUserCountChanged?.call(_currentUsers);

    // 启动定时器
    _startIncrementTimer();
  }

  /// 停止渐进式负载测试
  void stopProgressiveLoad() {
    _incrementTimer?.cancel();
    _incrementTimer = null;
    print('⏹️ 渐进式负载测试已停止');
    print('   最大稳定用户数: $_maxStableUsers');
  }

  /// 更新性能指标
  void updateMetrics(PerformanceMetrics metrics) {
    _metricsHistory.add(metrics);
    onMetricsUpdated?.call(metrics);

    // 分析性能指标
    _analyzePerformance(metrics);

    // 保持历史记录在合理范围内
    if (_metricsHistory.length > 1000) {
      _metricsHistory.removeAt(0);
    }
  }

  /// 启动递增定时器
  void _startIncrementTimer() {
    _incrementTimer = Timer.periodic(
      Duration(seconds: incrementIntervalSeconds),
      (timer) => _performIncrement(),
    );
  }

  /// 执行用户数递增
  void _performIncrement() {
    if (_hasReachedLimit || _currentUsers >= maxUsers) {
      if (!_hasReachedLimit) {
        _hasReachedLimit = true;
        print('✅ 已达到最大用户数限制: $maxUsers');
        onTestStopped?.call('已达到最大用户数限制');
      }
      return;
    }

    // 根据当前性能调整递增步长
    _adjustIncrementStep();

    // 计算新的用户数
    int newUsers = _currentUsers + _currentIncrementStep;
    newUsers = min(newUsers, maxUsers);

    print('📈 用户数递增: $_currentUsers -> $newUsers (步长: $_currentIncrementStep)');

    _currentUsers = newUsers;
    onUserCountChanged?.call(_currentUsers);
  }

  /// 分析性能指标
  void _analyzePerformance(PerformanceMetrics metrics) {
    final isHealthy = metrics.isHealthy(
      successRateThreshold,
      responseTimeThresholdMs,
    );

    if (isHealthy) {
      // 性能良好
      _consecutiveFailures = 0;
      if (_isStable) {
        _maxStableUsers = max(_maxStableUsers, metrics.currentUsers);
      }
      _isStable = true;

      print(
        '✅ 性能良好 - 用户数: ${metrics.currentUsers}, '
        '成功率: ${(metrics.successRate * 100).toStringAsFixed(1)}%, '
        '平均响应时间: ${metrics.averageResponseTime.toStringAsFixed(0)}ms',
      );
    } else {
      // 性能下降
      _consecutiveFailures++;
      _isStable = false;

      print(
        '⚠️ 性能下降 - 用户数: ${metrics.currentUsers}, '
        '成功率: ${(metrics.successRate * 100).toStringAsFixed(1)}%, '
        '平均响应时间: ${metrics.averageResponseTime.toStringAsFixed(0)}ms '
        '(连续失败: $_consecutiveFailures)',
      );

      // 检查是否需要停止测试
      if (_consecutiveFailures >= failureThresholdConsecutive) {
        _hasReachedLimit = true;
        print('🛑 性能持续下降，已达到服务器极限');
        print('   最大稳定用户数: $_maxStableUsers');
        onTestStopped?.call('性能持续下降，已达到服务器极限');
        stopProgressiveLoad();
      }
    }
  }

  /// 调整递增步长
  void _adjustIncrementStep() {
    if (_metricsHistory.isEmpty) return;

    final recentMetrics = _metricsHistory.take(5).toList();
    if (recentMetrics.isEmpty) return;

    // 计算最近的平均性能
    final avgSuccessRate =
        recentMetrics.map((m) => m.successRate).reduce((a, b) => a + b) /
        recentMetrics.length;

    final avgResponseTime =
        recentMetrics
            .map((m) => m.averageResponseTime)
            .reduce((a, b) => a + b) /
        recentMetrics.length;

    // 根据性能调整步长
    if (avgSuccessRate >= successRateThreshold &&
        avgResponseTime <= responseTimeThresholdMs * 0.7) {
      // 性能很好，可以加快递增
      _currentIncrementStep = min(
        (_currentIncrementStep * 1.5).round(),
        baseIncrementStep * 3,
      );
    } else if (avgSuccessRate >= successRateThreshold &&
        avgResponseTime <= responseTimeThresholdMs) {
      // 性能正常，保持当前步长
      _currentIncrementStep = baseIncrementStep;
    } else {
      // 性能有压力，减小步长
      _currentIncrementStep = max((_currentIncrementStep * 0.7).round(), 1);
    }
  }

  /// 获取性能趋势分析
  Map<String, dynamic> getPerformanceTrend() {
    if (_metricsHistory.length < 2) {
      return {'trend': 'insufficient_data', 'message': '数据不足，无法分析趋势'};
    }

    final recent = _metricsHistory.length > 10
        ? _metricsHistory.sublist(_metricsHistory.length - 10)
        : _metricsHistory;
    final older = _metricsHistory.length > 20
        ? _metricsHistory.sublist(
            _metricsHistory.length - 20,
            _metricsHistory.length - 10,
          )
        : _metricsHistory.take(_metricsHistory.length ~/ 2).toList();

    if (older.isEmpty) {
      return {'trend': 'insufficient_data', 'message': '历史数据不足，无法分析趋势'};
    }

    final recentAvgSuccess =
        recent.map((m) => m.successRate).reduce((a, b) => a + b) /
        recent.length;
    final olderAvgSuccess =
        older.map((m) => m.successRate).reduce((a, b) => a + b) / older.length;

    final recentAvgResponse =
        recent.map((m) => m.averageResponseTime).reduce((a, b) => a + b) /
        recent.length;
    final olderAvgResponse =
        older.map((m) => m.averageResponseTime).reduce((a, b) => a + b) /
        older.length;

    String trend;
    String message;

    if (recentAvgSuccess >= olderAvgSuccess &&
        recentAvgResponse <= olderAvgResponse * 1.1) {
      trend = 'improving';
      message = '性能正在改善或保持稳定';
    } else if (recentAvgSuccess < olderAvgSuccess * 0.95 ||
        recentAvgResponse > olderAvgResponse * 1.2) {
      trend = 'degrading';
      message = '性能正在下降';
    } else {
      trend = 'stable';
      message = '性能基本稳定';
    }

    return {
      'trend': trend,
      'message': message,
      'recent_success_rate': recentAvgSuccess,
      'older_success_rate': olderAvgSuccess,
      'recent_response_time': recentAvgResponse,
      'older_response_time': olderAvgResponse,
    };
  }

  /// 获取推荐的下一步操作
  String getRecommendation() {
    if (_hasReachedLimit) {
      return '已达到服务器极限，建议优化服务器配置或扩容';
    }

    if (_metricsHistory.isEmpty) {
      return '等待性能数据收集中...';
    }

    final latest = _metricsHistory.last;

    if (latest.successRate < successRateThreshold * 0.8) {
      return '成功率过低，建议检查服务器状态或减少负载';
    }

    if (latest.averageResponseTime > responseTimeThresholdMs * 1.5) {
      return '响应时间过长，建议检查服务器性能或网络状况';
    }

    if (_isStable && _consecutiveFailures == 0) {
      return '当前性能稳定，继续增加负载测试';
    }

    return '监控中，等待更多数据以提供建议';
  }
}
