import 'dart:io';
import 'dart:convert';
import 'package:intl/intl.dart';

/// VPS压力测试报告生成器
class VpsStressReportGenerator {
  static final DateFormat _dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
  static final DateFormat _fileNameFormat = DateFormat('yyyyMMdd_HHmmss');

  /// 生成完整的VPS压力测试报告
  static Future<void> generateCompleteReport({
    required Map<String, dynamic>? writeTestResults,
    required Map<String, dynamic>? readTestResults,
    required Map<String, dynamic>? websocketTestResults,
    required String serverName,
    bool generateMarkdown = true,
    bool generateJson = true,
    bool generateCsv = true,
  }) async {
    final timestamp = DateTime.now();
    final fileTimestamp = _fileNameFormat.format(timestamp);
    
    print('📄 生成VPS压力测试报告...');
    
    if (generateMarkdown) {
      await _generateMarkdownReport(
        writeTestResults: writeTestResults,
        readTestResults: readTestResults,
        websocketTestResults: websocketTestResults,
        serverName: serverName,
        timestamp: timestamp,
        fileName: 'vps_stress_test_report_${fileTimestamp}.md',
      );
    }
    
    if (generateJson) {
      await _generateJsonReport(
        writeTestResults: writeTestResults,
        readTestResults: readTestResults,
        websocketTestResults: websocketTestResults,
        serverName: serverName,
        timestamp: timestamp,
        fileName: 'vps_stress_test_report_${fileTimestamp}.json',
      );
    }
    
    if (generateCsv) {
      await _generateCsvReports(
        writeTestResults: writeTestResults,
        readTestResults: readTestResults,
        websocketTestResults: websocketTestResults,
        fileTimestamp: fileTimestamp,
      );
    }
    
    print('✅ VPS压力测试报告生成完成');
  }

  /// 生成Markdown报告
  static Future<void> _generateMarkdownReport({
    required Map<String, dynamic>? writeTestResults,
    required Map<String, dynamic>? readTestResults,
    required Map<String, dynamic>? websocketTestResults,
    required String serverName,
    required DateTime timestamp,
    required String fileName,
  }) async {
    final buffer = StringBuffer();
    
    // 报告标题
    buffer.writeln('# VPS服务器极限压力测试报告');
    buffer.writeln('');
    buffer.writeln('**测试时间**: ${_dateFormat.format(timestamp)}');
    buffer.writeln('**服务器**: $serverName');
    buffer.writeln('');
    
    // 执行摘要
    buffer.writeln('## 执行摘要');
    buffer.writeln('');
    _writeExecutiveSummary(buffer, writeTestResults, readTestResults, websocketTestResults);
    
    // 写入测试结果
    if (writeTestResults != null) {
      buffer.writeln('## 写入极限测试结果');
      buffer.writeln('');
      _writeTestSection(buffer, writeTestResults, '写入');
    }
    
    // 读取测试结果
    if (readTestResults != null) {
      buffer.writeln('## 读取极限测试结果');
      buffer.writeln('');
      _writeTestSection(buffer, readTestResults, '读取');
    }
    
    // WebSocket测试结果
    if (websocketTestResults != null) {
      buffer.writeln('## WebSocket连接极限测试结果');
      buffer.writeln('');
      _writeWebSocketSection(buffer, websocketTestResults);
    }
    
    // 性能分析和建议
    buffer.writeln('## 性能分析与优化建议');
    buffer.writeln('');
    _writePerformanceAnalysis(buffer, writeTestResults, readTestResults, websocketTestResults);
    
    // 服务器极限总结
    buffer.writeln('## 服务器极限总结');
    buffer.writeln('');
    _writeServerLimitsSummary(buffer, writeTestResults, readTestResults, websocketTestResults);
    
    // 写入文件
    final file = File(fileName);
    await file.writeAsString(buffer.toString());
    print('📝 Markdown报告已保存: $fileName');
  }

  /// 写入执行摘要
  static void _writeExecutiveSummary(
    StringBuffer buffer,
    Map<String, dynamic>? writeResults,
    Map<String, dynamic>? readResults,
    Map<String, dynamic>? websocketResults,
  ) {
    buffer.writeln('本次测试通过渐进式增加负载的方式，测试了VPS服务器在写入、读取和WebSocket连接方面的极限性能。');
    buffer.writeln('');
    
    if (writeResults != null) {
      final maxUsers = writeResults['max_stable_users'] ?? 0;
      final hasReachedLimit = writeResults['has_reached_limit'] ?? false;
      buffer.writeln('- **写入极限**: 最大稳定并发用户数 $maxUsers ${hasReachedLimit ? "(已达到极限)" : "(未达到极限)"}');
    }
    
    if (readResults != null) {
      final maxUsers = readResults['max_stable_users'] ?? 0;
      final hasReachedLimit = readResults['has_reached_limit'] ?? false;
      buffer.writeln('- **读取极限**: 最大稳定并发用户数 $maxUsers ${hasReachedLimit ? "(已达到极限)" : "(未达到极限)"}');
    }
    
    if (websocketResults != null) {
      final maxConnections = websocketResults['max_stable_connections'] ?? 0;
      final hasReachedLimit = websocketResults['has_reached_limit'] ?? false;
      buffer.writeln('- **WebSocket极限**: 最大稳定连接数 $maxConnections ${hasReachedLimit ? "(已达到极限)" : "(未达到极限)"}');
    }
    
    buffer.writeln('');
  }

  /// 写入测试部分
  static void _writeTestSection(StringBuffer buffer, Map<String, dynamic> results, String testType) {
    final stats = results['performance_stats'] as Map<String, dynamic>? ?? {};
    final maxUsers = results['max_stable_users'] ?? 0;
    final duration = results['test_duration_seconds'] ?? 0;
    final hasReachedLimit = results['has_reached_limit'] ?? false;
    
    buffer.writeln('### 测试概览');
    buffer.writeln('');
    buffer.writeln('| 指标 | 值 |');
    buffer.writeln('|------|-----|');
    buffer.writeln('| 测试持续时间 | ${duration}秒 (${(duration / 60).toStringAsFixed(1)}分钟) |');
    buffer.writeln('| 最大稳定用户数 | $maxUsers |');
    buffer.writeln('| 是否达到极限 | ${hasReachedLimit ? "是" : "否"} |');
    buffer.writeln('| 总请求数 | ${stats['total_requests'] ?? 0} |');
    buffer.writeln('| 成功率 | ${((stats['success_rate'] as double? ?? 0) * 100).toStringAsFixed(1)}% |');
    buffer.writeln('| 平均响应时间 | ${(stats['average_response_time'] as double? ?? 0).toStringAsFixed(0)}ms |');
    buffer.writeln('| P95响应时间 | ${(stats['p95_response_time'] as double? ?? 0).toStringAsFixed(0)}ms |');
    buffer.writeln('| 每秒请求数 | ${(stats['requests_per_second'] as double? ?? 0).toStringAsFixed(1)} |');
    buffer.writeln('');
    
    // 操作统计
    final operationStats = results['operation_stats'] as Map<String, dynamic>? ?? {};
    if (operationStats.isNotEmpty) {
      buffer.writeln('### 操作类型统计');
      buffer.writeln('');
      buffer.writeln('| 操作类型 | 总数 | 成功数 | 成功率 | 平均响应时间 | P95响应时间 |');
      buffer.writeln('|---------|------|--------|--------|-------------|-------------|');
      
      for (final entry in operationStats.entries) {
        final opName = entry.key;
        final opStats = entry.value as Map<String, dynamic>;
        final total = opStats['total'] ?? 0;
        final successful = opStats['successful'] ?? 0;
        final successRate = ((opStats['success_rate'] as double? ?? 0) * 100).toStringAsFixed(1);
        final avgTime = (opStats['average_response_time'] as double? ?? 0).toStringAsFixed(0);
        final p95Time = (opStats['p95_response_time'] as double? ?? 0).toStringAsFixed(0);
        
        buffer.writeln('| $opName | $total | $successful | $successRate% | ${avgTime}ms | ${p95Time}ms |');
      }
      buffer.writeln('');
    }
    
    // 建议
    final recommendation = results['recommendation'] as String? ?? '';
    if (recommendation.isNotEmpty) {
      buffer.writeln('### 建议');
      buffer.writeln('');
      buffer.writeln(recommendation);
      buffer.writeln('');
    }
  }

  /// 写入WebSocket部分
  static void _writeWebSocketSection(StringBuffer buffer, Map<String, dynamic> results) {
    final stats = results['performance_stats'] as Map<String, dynamic>? ?? {};
    final maxConnections = results['max_stable_connections'] ?? 0;
    final duration = results['test_duration_seconds'] ?? 0;
    final hasReachedLimit = results['has_reached_limit'] ?? false;
    
    buffer.writeln('### 测试概览');
    buffer.writeln('');
    buffer.writeln('| 指标 | 值 |');
    buffer.writeln('|------|-----|');
    buffer.writeln('| 测试持续时间 | ${duration}秒 (${(duration / 60).toStringAsFixed(1)}分钟) |');
    buffer.writeln('| 最大稳定连接数 | $maxConnections |');
    buffer.writeln('| 是否达到极限 | ${hasReachedLimit ? "是" : "否"} |');
    buffer.writeln('| 总操作数 | ${stats['total_requests'] ?? 0} |');
    buffer.writeln('| 成功率 | ${((stats['success_rate'] as double? ?? 0) * 100).toStringAsFixed(1)}% |');
    buffer.writeln('| 平均响应时间 | ${(stats['average_response_time'] as double? ?? 0).toStringAsFixed(0)}ms |');
    buffer.writeln('');
    
    // 连接统计
    final connectionStats = results['connection_stats'] as List<dynamic>? ?? [];
    if (connectionStats.isNotEmpty) {
      final activeConnections = connectionStats.where((c) => c['is_connected'] == true).length;
      final totalMessagesSent = connectionStats.fold<int>(0, (sum, c) => sum + (c['messages_sent'] as int? ?? 0));
      final totalMessagesReceived = connectionStats.fold<int>(0, (sum, c) => sum + (c['messages_received'] as int? ?? 0));
      final totalErrors = connectionStats.fold<int>(0, (sum, c) => sum + (c['errors_count'] as int? ?? 0));
      
      buffer.writeln('### 连接统计');
      buffer.writeln('');
      buffer.writeln('| 指标 | 值 |');
      buffer.writeln('|------|-----|');
      buffer.writeln('| 活跃连接数 | $activeConnections |');
      buffer.writeln('| 总发送消息数 | $totalMessagesSent |');
      buffer.writeln('| 总接收消息数 | $totalMessagesReceived |');
      buffer.writeln('| 总错误数 | $totalErrors |');
      buffer.writeln('');
    }
    
    // 建议
    final recommendation = results['recommendation'] as String? ?? '';
    if (recommendation.isNotEmpty) {
      buffer.writeln('### 建议');
      buffer.writeln('');
      buffer.writeln(recommendation);
      buffer.writeln('');
    }
  }

  /// 写入性能分析
  static void _writePerformanceAnalysis(
    StringBuffer buffer,
    Map<String, dynamic>? writeResults,
    Map<String, dynamic>? readResults,
    Map<String, dynamic>? websocketResults,
  ) {
    buffer.writeln('### 性能瓶颈分析');
    buffer.writeln('');
    
    // 分析各项测试的性能瓶颈
    if (writeResults != null) {
      final writeStats = writeResults['performance_stats'] as Map<String, dynamic>? ?? {};
      final writeSuccessRate = (writeStats['success_rate'] as double? ?? 0) * 100;
      final writeAvgTime = writeStats['average_response_time'] as double? ?? 0;
      
      if (writeSuccessRate < 95) {
        buffer.writeln('- **写入性能问题**: 成功率仅为${writeSuccessRate.toStringAsFixed(1)}%，建议检查数据库连接池配置和磁盘I/O性能');
      } else if (writeAvgTime > 1000) {
        buffer.writeln('- **写入响应时间问题**: 平均响应时间${writeAvgTime.toStringAsFixed(0)}ms过长，建议优化数据库索引和存储配置');
      } else {
        buffer.writeln('- **写入性能良好**: 成功率和响应时间都在可接受范围内');
      }
    }
    
    if (readResults != null) {
      final readStats = readResults['performance_stats'] as Map<String, dynamic>? ?? {};
      final readSuccessRate = (readStats['success_rate'] as double? ?? 0) * 100;
      final readAvgTime = readStats['average_response_time'] as double? ?? 0;
      
      if (readSuccessRate < 95) {
        buffer.writeln('- **读取性能问题**: 成功率仅为${readSuccessRate.toStringAsFixed(1)}%，建议增加数据库连接池大小');
      } else if (readAvgTime > 500) {
        buffer.writeln('- **读取响应时间问题**: 平均响应时间${readAvgTime.toStringAsFixed(0)}ms过长，建议添加数据库索引和启用查询缓存');
      } else {
        buffer.writeln('- **读取性能良好**: 成功率和响应时间都在可接受范围内');
      }
    }
    
    if (websocketResults != null) {
      final wsStats = websocketResults['performance_stats'] as Map<String, dynamic>? ?? {};
      final wsSuccessRate = (wsStats['success_rate'] as double? ?? 0) * 100;
      
      if (wsSuccessRate < 95) {
        buffer.writeln('- **WebSocket性能问题**: 成功率仅为${wsSuccessRate.toStringAsFixed(1)}%，建议检查网络配置和服务器内存使用');
      } else {
        buffer.writeln('- **WebSocket性能良好**: 连接稳定性在可接受范围内');
      }
    }
    
    buffer.writeln('');
    
    buffer.writeln('### 优化建议');
    buffer.writeln('');
    buffer.writeln('1. **硬件优化**:');
    buffer.writeln('   - 增加服务器内存以支持更多并发连接');
    buffer.writeln('   - 使用SSD存储提高磁盘I/O性能');
    buffer.writeln('   - 考虑使用多核CPU以提高并发处理能力');
    buffer.writeln('');
    buffer.writeln('2. **软件配置优化**:');
    buffer.writeln('   - 调整数据库连接池大小');
    buffer.writeln('   - 优化数据库索引策略');
    buffer.writeln('   - 启用适当的缓存机制');
    buffer.writeln('');
    buffer.writeln('3. **架构优化**:');
    buffer.writeln('   - 考虑使用负载均衡分散请求');
    buffer.writeln('   - 实施读写分离策略');
    buffer.writeln('   - 使用CDN加速静态资源');
    buffer.writeln('');
  }

  /// 写入服务器极限总结
  static void _writeServerLimitsSummary(
    StringBuffer buffer,
    Map<String, dynamic>? writeResults,
    Map<String, dynamic>? readResults,
    Map<String, dynamic>? websocketResults,
  ) {
    buffer.writeln('根据本次压力测试结果，该VPS服务器的性能极限如下：');
    buffer.writeln('');
    
    if (writeResults != null) {
      final maxUsers = writeResults['max_stable_users'] ?? 0;
      final hasReachedLimit = writeResults['has_reached_limit'] ?? false;
      buffer.writeln('- **写入能力**: 最多支持 $maxUsers 个并发用户同时写入数据${hasReachedLimit ? "" : "（可能支持更多）"}');
    }
    
    if (readResults != null) {
      final maxUsers = readResults['max_stable_users'] ?? 0;
      final hasReachedLimit = readResults['has_reached_limit'] ?? false;
      buffer.writeln('- **读取能力**: 最多支持 $maxUsers 个并发用户同时读取数据${hasReachedLimit ? "" : "（可能支持更多）"}');
    }
    
    if (websocketResults != null) {
      final maxConnections = websocketResults['max_stable_connections'] ?? 0;
      final hasReachedLimit = websocketResults['has_reached_limit'] ?? false;
      buffer.writeln('- **WebSocket能力**: 最多支持 $maxConnections 个并发WebSocket连接${hasReachedLimit ? "" : "（可能支持更多）"}');
    }
    
    buffer.writeln('');
    buffer.writeln('**注意**: 以上数据基于当前测试条件和服务器配置。实际生产环境中的性能可能因数据量、网络条件、服务器负载等因素而有所不同。');
  }

  /// 生成JSON报告
  static Future<void> _generateJsonReport({
    required Map<String, dynamic>? writeTestResults,
    required Map<String, dynamic>? readTestResults,
    required Map<String, dynamic>? websocketTestResults,
    required String serverName,
    required DateTime timestamp,
    required String fileName,
  }) async {
    final report = {
      'report_type': 'vps_stress_test',
      'server_name': serverName,
      'test_timestamp': timestamp.toIso8601String(),
      'write_test_results': writeTestResults,
      'read_test_results': readTestResults,
      'websocket_test_results': websocketTestResults,
      'summary': {
        'write_max_users': writeTestResults?['max_stable_users'] ?? 0,
        'read_max_users': readTestResults?['max_stable_users'] ?? 0,
        'websocket_max_connections': websocketTestResults?['max_stable_connections'] ?? 0,
      },
    };
    
    final file = File(fileName);
    await file.writeAsString(json.encode(report));
    print('📊 JSON报告已保存: $fileName');
  }

  /// 生成CSV报告
  static Future<void> _generateCsvReports({
    required Map<String, dynamic>? writeTestResults,
    required Map<String, dynamic>? readTestResults,
    required Map<String, dynamic>? websocketTestResults,
    required String fileTimestamp,
  }) async {
    if (writeTestResults != null) {
      await _generateCsvForTest(writeTestResults, 'write_test_${fileTimestamp}.csv');
    }
    
    if (readTestResults != null) {
      await _generateCsvForTest(readTestResults, 'read_test_${fileTimestamp}.csv');
    }
    
    if (websocketTestResults != null) {
      await _generateCsvForTest(websocketTestResults, 'websocket_test_${fileTimestamp}.csv');
    }
  }

  /// 为单个测试生成CSV
  static Future<void> _generateCsvForTest(Map<String, dynamic> results, String fileName) async {
    final buffer = StringBuffer();
    
    // CSV头部
    buffer.writeln('metric,value');
    
    // 基本指标
    buffer.writeln('test_type,${results['test_type']}');
    buffer.writeln('max_stable_users,${results['max_stable_users'] ?? results['max_stable_connections'] ?? 0}');
    buffer.writeln('test_duration_seconds,${results['test_duration_seconds']}');
    buffer.writeln('has_reached_limit,${results['has_reached_limit']}');
    
    // 性能统计
    final stats = results['performance_stats'] as Map<String, dynamic>? ?? {};
    for (final entry in stats.entries) {
      buffer.writeln('${entry.key},${entry.value}');
    }
    
    final file = File(fileName);
    await file.writeAsString(buffer.toString());
    print('📈 CSV报告已保存: $fileName');
  }
}
