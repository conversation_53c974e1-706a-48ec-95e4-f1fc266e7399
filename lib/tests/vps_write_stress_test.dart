import 'dart:async';
import 'dart:math';
import '../config/vps_stress_test_config.dart';
import '../services/backend_service.dart';
import '../models/test_record.dart';
import '../utils/data_generator.dart';
import '../utils/progressive_load_controller.dart';
import '../utils/real_time_monitor.dart';

/// 虚拟用户写入器
class VirtualWriteUser {
  final int userId;
  final BackendService service;
  final String tableName;
  final VpsStressTestConfig config;
  final RealTimeMonitor monitor;

  bool _isActive = false;
  Timer? _actionTimer;
  int _recordsWritten = 0;
  int _errorsCount = 0;

  VirtualWriteUser({
    required this.userId,
    required this.service,
    required this.tableName,
    required this.config,
    required this.monitor,
  });

  /// 开始写入操作
  void startWriting() {
    if (_isActive) return;

    _isActive = true;
    _recordsWritten = 0;
    _errorsCount = 0;

    _scheduleNextWrite();
  }

  /// 停止写入操作
  void stopWriting() {
    _isActive = false;
    _actionTimer?.cancel();
    _actionTimer = null;
  }

  /// 调度下一次写入
  void _scheduleNextWrite() {
    if (!_isActive) return;

    final interval = config.getRandomWriteActionInterval();
    _actionTimer = Timer(Duration(milliseconds: interval), () {
      _performWrite();
    });
  }

  /// 执行写入操作
  Future<void> _performWrite() async {
    if (!_isActive) return;

    try {
      final startTime = DateTime.now();

      // 生成测试数据（静默模式）
      final records = DataGenerator.generateRecords(config.batchSize);

      // 执行写入
      List<TestRecord>? results;
      if (config.batchSize == 1) {
        final result = await service.insertRecord(tableName, records.first);
        results = result != null ? [result] : null;
      } else {
        results = await service.insertRecords(tableName, records);
      }

      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime).inMilliseconds;

      // 记录结果
      final success = results != null && results.isNotEmpty;
      monitor.recordRequest(
        RequestResult(
          success: success,
          responseTimeMs: responseTime,
          timestamp: endTime,
          operation: 'write',
          error: success ? null : 'Write failed',
        ),
      );

      if (success) {
        _recordsWritten += results!.length;
      } else {
        _errorsCount++;
      }
    } catch (e) {
      final endTime = DateTime.now();
      _errorsCount++;

      monitor.recordRequest(
        RequestResult(
          success: false,
          responseTimeMs: config.requestTimeoutSeconds * 1000,
          timestamp: endTime,
          operation: 'write',
          error: e.toString(),
        ),
      );
    }

    // 调度下一次写入
    _scheduleNextWrite();
  }

  /// 获取用户统计信息
  Map<String, dynamic> getStats() {
    return {
      'user_id': userId,
      'records_written': _recordsWritten,
      'errors_count': _errorsCount,
      'is_active': _isActive,
    };
  }
}

/// VPS写入压力测试
class VpsWriteStressTest {
  final VpsStressTestConfig config;
  final BackendService service;
  final String tableName;

  late final RealTimeMonitor _monitor;
  late final ProgressiveLoadController _loadController;

  final List<VirtualWriteUser> _users = [];
  bool _isRunning = false;
  DateTime? _testStartTime;
  DateTime? _testEndTime;

  // 回调函数
  Function(String message)? onStatusUpdate;
  Function(PerformanceMetrics metrics)? onMetricsUpdate;
  Function(String alert)? onAlert;

  VpsWriteStressTest({
    required this.config,
    required this.service,
    required this.tableName,
  }) {
    _initializeMonitor();
    _initializeLoadController();
  }

  /// 初始化监控器
  void _initializeMonitor() {
    _monitor = RealTimeMonitor(
      metricsCollectionIntervalSeconds: config.metricsCollectionIntervalSeconds,
      performanceCheckIntervalSeconds: config.performanceCheckIntervalSeconds,
      successRateThreshold: config.successRateThreshold,
      responseTimeThresholdMs: config.responseTimeThresholdMs,
    );

    _monitor.onMetricsCollected = (metrics) {
      // 更新用户数信息
      final updatedMetrics = PerformanceMetrics(
        currentUsers: _users.where((u) => u._isActive).length,
        successRate: metrics.successRate,
        averageResponseTime: metrics.averageResponseTime,
        p95ResponseTime: metrics.p95ResponseTime,
        timeoutCount: metrics.timeoutCount,
        errorCount: metrics.errorCount,
        totalRequests: metrics.totalRequests,
        timestamp: metrics.timestamp,
      );

      _loadController.updateMetrics(updatedMetrics);
      onMetricsUpdate?.call(updatedMetrics);
    };

    _monitor.onPerformanceAlert = (alert) {
      onAlert?.call(alert);
    };
  }

  /// 初始化负载控制器
  void _initializeLoadController() {
    _loadController = ProgressiveLoadController(
      initialUsers: config.writeInitialUsers,
      maxUsers: config.writeMaxUsers,
      baseIncrementStep: config.writeUserIncrementStep,
      incrementIntervalSeconds: config.writeIncrementIntervalSeconds,
      successRateThreshold: config.successRateThreshold,
      responseTimeThresholdMs: config.responseTimeThresholdMs,
      failureThresholdConsecutive: config.failureThresholdConsecutive,
    );

    _loadController.onUserCountChanged = (newUserCount) {
      _adjustUserCount(newUserCount);
    };

    _loadController.onTestStopped = (reason) {
      onStatusUpdate?.call('测试停止: $reason');
      _stopTest();
    };
  }

  /// 开始写入压力测试
  Future<void> startTest() async {
    if (_isRunning) {
      print('写入压力测试已在运行中');
      return;
    }

    print('🚀 开始VPS写入压力测试');
    print('   服务器: ${service.name}');
    print('   表名: $tableName');

    _isRunning = true;
    _testStartTime = DateTime.now();

    // 初始化服务
    if (!await service.initialize()) {
      print('❌ 服务初始化失败');
      return;
    }

    // 创建测试表
    if (!await service.createTestTable(tableName)) {
      print('❌ 创建测试表失败');
      return;
    }

    onStatusUpdate?.call('写入压力测试已开始');

    // 启动监控
    _monitor.startMonitoring();

    // 启动渐进式负载
    _loadController.startProgressiveLoad();

    // 等待测试完成或超时
    await _waitForTestCompletion();
  }

  /// 停止测试
  void _stopTest() {
    if (!_isRunning) return;

    _isRunning = false;
    _testEndTime = DateTime.now();

    // 停止所有用户
    for (final user in _users) {
      user.stopWriting();
    }

    // 停止监控和负载控制
    _monitor.stopMonitoring();
    _loadController.stopProgressiveLoad();

    onStatusUpdate?.call('写入压力测试已完成');

    print('✅ VPS写入压力测试完成');
    _printTestSummary();
  }

  /// 调整用户数量
  void _adjustUserCount(int targetUserCount) {
    final currentActiveUsers = _users.where((u) => u._isActive).length;

    if (targetUserCount > currentActiveUsers) {
      // 增加用户
      final usersToAdd = targetUserCount - currentActiveUsers;
      for (int i = 0; i < usersToAdd; i++) {
        final userId = _users.length + 1;
        final user = VirtualWriteUser(
          userId: userId,
          service: service,
          tableName: tableName,
          config: config,
          monitor: _monitor,
        );

        _users.add(user);
        user.startWriting();
      }

      onStatusUpdate?.call('增加写入用户: $currentActiveUsers -> $targetUserCount');
    } else if (targetUserCount < currentActiveUsers) {
      // 减少用户
      final usersToStop = currentActiveUsers - targetUserCount;
      int stopped = 0;

      for (final user in _users.reversed) {
        if (user._isActive && stopped < usersToStop) {
          user.stopWriting();
          stopped++;
        }
      }

      onStatusUpdate?.call('减少写入用户: $currentActiveUsers -> $targetUserCount');
    }
  }

  /// 等待测试完成
  Future<void> _waitForTestCompletion() async {
    final testDuration = Duration(minutes: config.testDurationMinutes);
    final endTime = _testStartTime!.add(testDuration);

    while (_isRunning && DateTime.now().isBefore(endTime)) {
      await Future.delayed(const Duration(seconds: 1));
    }

    if (_isRunning) {
      onStatusUpdate?.call('测试时间到达，正在停止测试');
      _stopTest();
    }
  }

  /// 打印测试摘要
  void _printTestSummary() {
    if (_testStartTime == null) return;

    final duration = (_testEndTime ?? DateTime.now()).difference(
      _testStartTime!,
    );
    final stats = _monitor.getCurrentStats();

    print('\n=== VPS写入压力测试摘要 ===');
    print('服务器: ${service.name}');
    print('测试持续时间: ${duration.inMinutes}分${duration.inSeconds % 60}秒');
    print('最大并发用户数: ${_loadController.maxStableUsers}');
    print('总请求数: ${stats['total_requests']}');
    print(
      '成功率: ${((stats['success_rate'] as double) * 100).toStringAsFixed(1)}%',
    );
    print(
      '平均响应时间: ${(stats['average_response_time'] as double).toStringAsFixed(0)}ms',
    );
    print(
      'P95响应时间: ${(stats['p95_response_time'] as double).toStringAsFixed(0)}ms',
    );
    print(
      '每秒请求数: ${(stats['requests_per_second'] as double).toStringAsFixed(1)}',
    );

    final recommendation = _loadController.getRecommendation();
    print('建议: $recommendation');
    print('');
  }

  /// 获取测试结果
  Map<String, dynamic> getTestResults() {
    final duration = _testStartTime != null
        ? (_testEndTime ?? DateTime.now()).difference(_testStartTime!)
        : Duration.zero;

    final stats = _monitor.getCurrentStats();
    final operationStats = _monitor.getOperationStats();
    final recentErrors = _monitor.getRecentErrors();

    return {
      'test_type': 'write_stress_test',
      'server_name': service.name,
      'test_duration_seconds': duration.inSeconds,
      'max_stable_users': _loadController.maxStableUsers,
      'has_reached_limit': _loadController.hasReachedLimit,
      'performance_stats': stats,
      'operation_stats': operationStats,
      'recent_errors': recentErrors,
      'recommendation': _loadController.getRecommendation(),
      'performance_trend': _loadController.getPerformanceTrend(),
      'user_stats': _users.map((u) => u.getStats()).toList(),
      'test_config': {
        'initial_users': config.writeInitialUsers,
        'max_users': config.writeMaxUsers,
        'increment_step': config.writeUserIncrementStep,
        'records_per_user': config.recordsPerUser,
        'batch_size': config.batchSize,
      },
    };
  }

  /// 强制停止测试
  void forceStop() {
    onStatusUpdate?.call('强制停止写入压力测试');
    _stopTest();
  }
}
