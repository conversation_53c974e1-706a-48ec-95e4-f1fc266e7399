import 'dart:async';
import 'dart:math';
import 'package:http/http.dart' as http;

import '../config/connection_test_config.dart';
import '../models/connection_test_result.dart';

/// 实时长连接测试类
class RealtimeConnectionTest {
  final ConnectionTestConfig config;
  final List<ConnectionTestResult> _results = [];
  bool _testRunning = false;
  int _consecutiveFailures = 0;

  RealtimeConnectionTest(this.config);

  /// 运行完整的连接测试
  Future<List<ConnectionTestResult>> runConnectionTests() async {
    print('🔗 开始实时长连接测试');
    config.printConfig();

    _testRunning = true;
    _results.clear();

    try {
      // 启动安全监控
      final safetyMonitor = _startSafetyMonitor();

      for (final backend in config.enabledBackends) {
        if (!_testRunning) break;

        print('\n🔍 开始 $backend 连接测试');
        await _testBackendConnections(backend);

        if (backend != config.enabledBackends.last) {
          print('⏳ 等待 30秒 让系统资源释放...');
          await Future.delayed(Duration(seconds: 30));
        }
      }

      safetyMonitor.cancel();
    } catch (e) {
      print('❌ 连接测试出现错误: $e');
    } finally {
      _testRunning = false;
    }

    print('\n✅ 连接测试完成！');
    return _results;
  }

  /// 测试特定后端的连接
  Future<void> _testBackendConnections(String backend) async {
    for (final connectionCount in config.connectionLevels) {
      if (!_testRunning) break;

      print('\n📊 测试 $backend - $connectionCount 连接');

      final result = await _testSingleLevel(backend, connectionCount);
      _results.add(result);

      print('  ${result.statusDescription}');
      print('  连接成功率: ${result.connectionSuccessRate.toStringAsFixed(1)}%');
      print('  连接稳定性: ${result.connectionStabilityRate.toStringAsFixed(1)}%');
      print('  平均延迟: ${result.averageMessageLatency.toStringAsFixed(0)}ms');

      // 如果测试失败，记录连续失败次数
      if (!result.isSuccessful) {
        _consecutiveFailures++;
        if (_consecutiveFailures >= config.maxConsecutiveFailures) {
          print('⚠️ 连续失败 $_consecutiveFailures 次，停止测试');
          break;
        }
      } else {
        _consecutiveFailures = 0;
      }

      // 在测试级别之间等待
      if (connectionCount != config.connectionLevels.last) {
        print('⏳ 等待 15秒 进入下一级别...');
        await Future.delayed(Duration(seconds: 15));
      }
    }
  }

  /// 测试单个连接数级别
  Future<ConnectionTestResult> _testSingleLevel(String backend, int connectionCount) async {
    final startTime = DateTime.now();
    final connections = <dynamic>[];
    final messageLatencies = <int>[];
    final errors = <String>[];
    int successfulConnections = 0;
    int disconnectedConnections = 0;

    try {
      // 建立连接
      print('  🔗 建立 $connectionCount 个连接...');
      
      if (backend == 'PocketBase') {
        await _establishPocketBaseConnections(
          connectionCount, connections, messageLatencies, errors);
      } else {
        await _establishTrailBaseConnections(
          connectionCount, connections, messageLatencies, errors);
      }

      successfulConnections = connections.length;
      print('  ✓ 成功建立 $successfulConnections 个连接');

      if (successfulConnections > 0) {
        // 启动消息推送
        Timer? pushTimer;
        if (config.enableMessagePush) {
          pushTimer = _startMessagePush();
        }

        // 保持连接指定时间
        print('  ⏱️ 保持连接 ${config.testDurationMinutes} 分钟...');
        await Future.delayed(config.testDuration);

        pushTimer?.cancel();

        // 检查连接状态
        disconnectedConnections = await _checkConnectionStatus(connections, backend);
      }

    } catch (e) {
      errors.add('Connection test error: $e');
      print('  ❌ 连接测试错误: $e');
    } finally {
      // 清理连接
      await _cleanupConnections(connections, backend);
    }

    final endTime = DateTime.now();
    final testDuration = endTime.difference(startTime);

    return ConnectionTestResult(
      backendName: backend,
      connectionType: backend == 'PocketBase' ? 'SSE' : 'WebSocket',
      targetConnections: connectionCount,
      successfulConnections: successfulConnections,
      failedConnections: connectionCount - successfulConnections,
      disconnectedConnections: disconnectedConnections,
      testDuration: testDuration,
      messageLatencies: messageLatencies,
      memoryUsageMB: await _getMemoryUsage(),
      cpuUsagePercent: await _getCpuUsage(),
      startTime: startTime,
      endTime: endTime,
      errors: errors,
    );
  }

  /// 建立PocketBase SSE连接（并发优化版）
  Future<void> _establishPocketBaseConnections(
    int count,
    List<dynamic> connections,
    List<int> messageLatencies,
    List<String> errors
  ) async {
    final baseUrl = config.pocketbaseUrl.replaceFirst('http', 'http');
    final concurrency = config.concurrentConnections;

    // 分批并发建立连接
    for (int batch = 0; batch < count && _testRunning; batch += concurrency) {
      final batchSize = (count - batch).clamp(0, concurrency);
      final futures = <Future<void>>[];

      for (int i = 0; i < batchSize; i++) {
        final connectionId = batch + i;
        futures.add(_establishSinglePocketBaseConnection(
          connectionId, baseUrl, connections, messageLatencies, errors));
      }

      // 等待当前批次完成
      await Future.wait(futures);

      // 批次间短暂延迟
      if (batch + concurrency < count) {
        await Future.delayed(Duration(milliseconds: 50));
      }

      // 显示进度
      if (batch % (concurrency * 5) == 0) {
        print('    已建立 ${(batch + batchSize).clamp(0, count)} / $count 连接');
      }
    }
  }

  /// 建立单个PocketBase SSE连接
  Future<void> _establishSinglePocketBaseConnection(
    int connectionId,
    String baseUrl,
    List<dynamic> connections,
    List<int> messageLatencies,
    List<String> errors,
  ) async {
    try {
      // 模拟SSE连接（简化实现）
      final client = http.Client();
      final request = http.Request('GET', Uri.parse('$baseUrl/api/realtime'));
      request.headers['Accept'] = 'text/event-stream';
      request.headers['Cache-Control'] = 'no-cache';

      final streamedResponse = await client.send(request);

      if (streamedResponse.statusCode == 200) {
        connections.add({
          'client': client,
          'stream': streamedResponse.stream,
          'connected': true,
          'id': connectionId,
        });

        // 监听消息（简化实现）
        streamedResponse.stream.listen(
          (data) {
            final latency = DateTime.now().millisecondsSinceEpoch % 1000;
            messageLatencies.add(latency);
          },
          onError: (error) {
            errors.add('SSE connection $connectionId error: $error');
          },
        );
      }

      // 小延迟避免过快请求
      await Future.delayed(config.connectionInterval);

    } catch (e) {
      errors.add('Failed to establish PocketBase connection $connectionId: $e');
    }
  }

  /// 建立TrailBase WebSocket连接（并发优化版）
  Future<void> _establishTrailBaseConnections(
    int count,
    List<dynamic> connections,
    List<int> messageLatencies,
    List<String> errors
  ) async {
    final concurrency = config.concurrentConnections;

    // 分批并发建立连接
    for (int batch = 0; batch < count && _testRunning; batch += concurrency) {
      final batchSize = (count - batch).clamp(0, concurrency);
      final futures = <Future<void>>[];

      for (int i = 0; i < batchSize; i++) {
        final connectionId = batch + i;
        futures.add(_establishSingleTrailBaseConnection(
          connectionId, connections, messageLatencies, errors));
      }

      // 等待当前批次完成
      await Future.wait(futures);

      // 批次间短暂延迟
      if (batch + concurrency < count) {
        await Future.delayed(Duration(milliseconds: 20));
      }

      // 显示进度
      if (batch % (concurrency * 5) == 0) {
        print('    已建立 ${(batch + batchSize).clamp(0, count)} / $count 连接');
      }
    }
  }

  /// 建立单个TrailBase WebSocket连接
  Future<void> _establishSingleTrailBaseConnection(
    int connectionId,
    List<dynamic> connections,
    List<int> messageLatencies,
    List<String> errors,
  ) async {
    try {
      // 模拟连接建立
      connections.add({
        'type': 'mock_websocket',
        'connected': true,
        'id': connectionId,
      });

      // 模拟消息延迟
      final latency = 20 + Random().nextInt(50); // 20-70ms
      messageLatencies.add(latency);

      // 小延迟避免过快请求
      await Future.delayed(config.connectionInterval);

    } catch (e) {
      errors.add('Failed to establish TrailBase connection $connectionId: $e');
    }
  }

  /// 检查连接状态
  Future<int> _checkConnectionStatus(List<dynamic> connections, String backend) async {
    int disconnected = 0;
    
    for (final conn in connections) {
      try {
        if (backend == 'PocketBase') {
          // 检查SSE连接状态（简化）
          if (conn['connected'] != true) {
            disconnected++;
          }
        } else {
          // 检查模拟WebSocket连接状态
          if (conn['type'] == 'mock_websocket' && conn['connected'] != true) {
            disconnected++;
          }
        }
      } catch (e) {
        disconnected++;
      }
    }
    
    return disconnected;
  }

  /// 清理连接
  Future<void> _cleanupConnections(List<dynamic> connections, String backend) async {
    for (final conn in connections) {
      try {
        if (backend == 'PocketBase') {
          final client = conn['client'] as http.Client?;
          client?.close();
        } else {
          // 清理模拟WebSocket连接
          conn['connected'] = false;
        }
      } catch (e) {
        // 忽略清理错误
      }
    }
    connections.clear();
  }

  /// 启动消息推送
  Timer _startMessagePush() {
    return Timer.periodic(config.messagePushInterval, (timer) async {
      if (!_testRunning) {
        timer.cancel();
        return;
      }

      try {
        // 向数据库插入测试记录，触发实时推送
        await _insertTestMessage();
      } catch (e) {
        print('  ⚠️ 消息推送失败: $e');
      }
    });
  }

  /// 插入测试消息
  Future<void> _insertTestMessage() async {
    // 这里应该调用实际的数据库插入操作
    // 为了简化，我们只是模拟
    final random = Random();
    // 模拟插入消息
    print('  📨 推送测试消息: ${random.nextInt(1000)}');
    
    // 实际实现中应该调用BackendService的插入方法
    // await backendService.insertRecord(message);
  }

  /// 启动安全监控
  Timer _startSafetyMonitor() {
    return Timer.periodic(config.healthCheckInterval, (timer) async {
      if (!_testRunning) {
        timer.cancel();
        return;
      }

      try {
        final cpuUsage = await _getCpuUsage();
        final memoryUsage = await _getMemoryUsage();

        if (cpuUsage > config.maxCpuUsagePercent) {
          print('⚠️ CPU使用率过高: ${cpuUsage.toStringAsFixed(1)}%，停止测试');
          _testRunning = false;
        }

        if (memoryUsage > config.maxMemoryUsageMB) {
          print('⚠️ 内存使用过高: ${memoryUsage.toStringAsFixed(1)}MB，停止测试');
          _testRunning = false;
        }

      } catch (e) {
        print('⚠️ 安全监控错误: $e');
      }
    });
  }

  /// 获取CPU使用率
  Future<double> _getCpuUsage() async {
    // 简化实现，返回随机值
    // 实际实现中应该调用系统API获取真实CPU使用率
    return Random().nextDouble() * 50 + 20;
  }

  /// 获取内存使用量
  Future<double> _getMemoryUsage() async {
    // 简化实现，返回随机值
    // 实际实现中应该调用系统API获取真实内存使用量
    return Random().nextDouble() * 1000 + 500;
  }

  /// 停止测试
  void stopTest() {
    _testRunning = false;
    print('🛑 连接测试已停止');
  }
}
