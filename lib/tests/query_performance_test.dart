import 'dart:math';
import '../services/backend_service.dart';
import '../config/test_config.dart';

/// 查询测试结果
class QueryTestResult {
  final String testName;
  final Duration duration;
  final int successfulQueries;
  final int totalQueries;

  QueryTestResult({
    required this.testName,
    required this.duration,
    required this.successfulQueries,
    required this.totalQueries,
  });

  double get queriesPerSecond => duration.inMilliseconds > 0
      ? (successfulQueries * 1000.0) / duration.inMilliseconds
      : 0.0;
}

/// 查询性能测试类
class QueryPerformanceTest {
  final List<BackendService> _services = [];
  final Map<String, QueryTestResult> _results = {};
  String _tableName = 'performance_test';
  final Random _random = Random();

  /// 添加后端服务
  void addService(BackendService service) {
    _services.add(service);
  }

  /// 运行完整的查询性能测试套件
  Future<void> runQueryTestSuite(TestConfig config) async {
    print('=== 开始查询性能测试 ===');
    print('查询迭代次数: ${config.queryIterations}');
    print('查询批次大小: ${config.queryBatchSize}');
    print('');

    // 对每个后端服务运行查询测试
    for (int i = 0; i < _services.length; i++) {
      final service = _services[i];
      await _runServiceQueryTests(service, config);

      // 如果不是最后一个服务，等待间隔时间
      if (i < _services.length - 1) {
        print('\n⏳ 等待 ${config.intervalSeconds}秒 让系统资源释放...');
        await Future.delayed(Duration(seconds: config.intervalSeconds));
        print('✓ 等待完成，继续下一个后端查询测试\n');
      } else {
        print('');
      }
    }

    // 不再生成单独的查询报告，统一在main中生成
    print('=== 查询性能测试完成 ===');
  }

  /// 运行查询测试套件并返回结果
  Future<Map<String, QueryTestResult>> runQueryTestSuiteWithResults(TestConfig config) async {
    print('=== 开始查询性能测试 ===');
    print('查询迭代次数: ${config.queryIterations}');
    print('查询批次大小: ${config.queryBatchSize}');
    print('');

    // 对每个后端服务运行查询测试
    for (int i = 0; i < _services.length; i++) {
      final service = _services[i];
      await _runServiceQueryTests(service, config);

      // 如果不是最后一个服务，等待间隔时间
      if (i < _services.length - 1) {
        print('\n⏳ 等待 ${config.intervalSeconds}秒 让系统资源释放...');
        await Future.delayed(Duration(seconds: config.intervalSeconds));
        print('✓ 等待完成，继续下一个后端查询测试\n');
      } else {
        print('');
      }
    }

    return Map.from(_results);
  }

  /// 运行单个服务的查询测试
  Future<void> _runServiceQueryTests(
      BackendService service, TestConfig config) async {
    print('🔍 开始 ${service.name} 查询性能测试');

    try {
      // 初始化服务
      if (!await service.initialize()) {
        print('✗ ${service.name} 初始化失败');
        return;
      }

      // 使用固定表名
      String actualTableName = _tableName;

      // 获取总记录数
      final totalRecords = await service.getTotalRecordCount(actualTableName);
      print('📊 ${service.name} 总记录数: $totalRecords');

      if (totalRecords == 0) {
        print('⚠️ ${service.name} 没有数据，跳过查询测试');
        return;
      }

      // 运行各种查询测试
      if (config.enableIdQuery) {
        await _runIdQueryTest(service, config, totalRecords, actualTableName);
      }

      if (config.enableConditionQuery) {
        await _runConditionQueryTest(service, config, actualTableName);
      }

      if (config.enableRangeQuery) {
        await _runRangeQueryTest(service, config, actualTableName);
      }

      if (config.enablePaginationQuery) {
        await _runPaginationQueryTest(
            service, config, totalRecords, actualTableName);
      }

      if (config.enableAggregationQuery) {
        await _runAggregationQueryTest(service, config, actualTableName);
      }

      // 多级别并发查询测试
      await _runMultiLevelConcurrentQueryTests(service, config, totalRecords, actualTableName);
    } catch (e) {
      print('✗ ${service.name} 查询测试失败: $e');
    } finally {
      await service.close();
    }
  }

  /// ID查询测试
  Future<void> _runIdQueryTest(BackendService service, TestConfig config,
      int totalRecords, String tableName) async {
    print('  🔍 ID查询测试...');

    final testName = '${service.name}_id_query';
    final startTime = DateTime.now();

    int successCount = 0;
    final iterations = config.queryIterations;

    // 先获取一些实际存在的记录来获取真实的ID
    final sampleRecords = await service.getRecordsPaginated(tableName, 0, 50);
    if (sampleRecords.isEmpty) {
      print('    ⚠️ 无法获取样本记录，跳过ID查询测试');
      return;
    }

    for (int i = 0; i < iterations; i++) {
      // 从样本记录中随机选择一个真实的ID进行查询
      final randomRecord = sampleRecords[_random.nextInt(sampleRecords.length)];
      if (randomRecord.id != null) {
        final record = await service.getRecordById(tableName, randomRecord.id!);
        if (record != null) {
          successCount++;
        }
      }
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    final result = QueryTestResult(
      testName: testName,
      duration: duration,
      successfulQueries: successCount,
      totalQueries: iterations,
    );

    _results[testName] = result;

    print(
        '    ✓ ID查询: ${result.queriesPerSecond.toStringAsFixed(2)} queries/s (${successCount}/${iterations} 成功)');
  }

  /// 条件查询测试
  Future<void> _runConditionQueryTest(
      BackendService service, TestConfig config, String tableName) async {
    print('  🔍 条件查询测试...');

    final testName = '${service.name}_condition_query';
    final startTime = DateTime.now();

    int successCount = 0;
    final iterations = (config.queryIterations ~/ 2).clamp(10, config.queryIterations); // 增加迭代次数

    final conditions = [
      {'age': 25},
      {'age': 30},
      {'age': 35},
      {'age': 40},
      {'age': 45},
    ];

    for (int i = 0; i < iterations; i++) {
      final condition = conditions[i % conditions.length];

      final records =
          await service.getRecordsByCondition(tableName, condition, limit: 100);
      if (records.isNotEmpty) {
        successCount++;
      }
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    final result = QueryTestResult(
      testName: testName,
      duration: duration,
      successfulQueries: successCount,
      totalQueries: iterations,
    );

    _results[testName] = result;

    print(
        '    ✓ 条件查询: ${result.queriesPerSecond.toStringAsFixed(2)} queries/s (${successCount}/${iterations} 成功)');
  }

  /// 范围查询测试
  Future<void> _runRangeQueryTest(
      BackendService service, TestConfig config, String tableName) async {
    print('  🔍 范围查询测试...');

    final testName = '${service.name}_range_query';
    final startTime = DateTime.now();

    int successCount = 0;
    final iterations = (config.queryIterations ~/ 2).clamp(10, config.queryIterations); // 增加迭代次数

    for (int i = 0; i < iterations; i++) {
      if (i % 2 == 0) {
        // 年龄范围查询
        final minAge = 20 + _random.nextInt(20);
        final maxAge = minAge + 10;

        final records = await service
            .getRecordsByAgeRange(tableName, minAge, maxAge, limit: 100);
        if (records.isNotEmpty) {
          successCount++;
        }
      } else {
        // 分数范围查询
        final minScore = 50.0 + _random.nextDouble() * 30;
        final maxScore = minScore + 20;

        final records = await service
            .getRecordsByScoreRange(tableName, minScore, maxScore, limit: 100);
        if (records.isNotEmpty) {
          successCount++;
        }
      }
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    final result = QueryTestResult(
      testName: testName,
      duration: duration,
      successfulQueries: successCount,
      totalQueries: iterations,
    );

    _results[testName] = result;

    print(
        '    ✓ 范围查询: ${result.queriesPerSecond.toStringAsFixed(2)} queries/s (${successCount}/${iterations} 成功)');
  }

  /// 分页查询测试
  Future<void> _runPaginationQueryTest(BackendService service,
      TestConfig config, int totalRecords, String tableName) async {
    print('  🔍 分页查询测试...');

    final testName = '${service.name}_pagination_query';
    final startTime = DateTime.now();

    int successCount = 0;
    final iterations = config.queryIterations.clamp(20, config.queryIterations); // 增加迭代次数
    final pageSize = config.queryBatchSize;

    for (int i = 0; i < iterations; i++) {
      // 确保offset至少为1，避免0值导致的错误
      final maxOffset = totalRecords > pageSize ? totalRecords - pageSize : 0;
      final offset = maxOffset > 0 ? _random.nextInt(maxOffset) + 1 : 1;

      final records =
          await service.getRecordsPaginated(tableName, offset, pageSize);
      if (records.isNotEmpty) {
        successCount++;
      }
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    final result = QueryTestResult(
      testName: testName,
      duration: duration,
      successfulQueries: successCount,
      totalQueries: iterations,
    );

    _results[testName] = result;

    print(
        '    ✓ 分页查询: ${result.queriesPerSecond.toStringAsFixed(2)} queries/s (${successCount}/${iterations} 成功)');
  }

  /// 聚合查询测试
  Future<void> _runAggregationQueryTest(
      BackendService service, TestConfig config, String tableName) async {
    print('  🔍 聚合查询测试...');

    final testName = '${service.name}_aggregation_query';
    final startTime = DateTime.now();

    int successCount = 0;
    final iterations = (config.queryIterations ~/ 5).clamp(5, config.queryIterations); // 增加聚合查询迭代次数

    for (int i = 0; i < iterations; i++) {
      switch (i % 4) {
        case 0:
          final avg = await service.getAverageScore(tableName);
          if (avg != null) successCount++;
          break;
        case 1:
          final max = await service.getMaxScore(tableName);
          if (max != null) successCount++;
          break;
        case 2:
          final min = await service.getMinScore(tableName);
          if (min != null) successCount++;
          break;
        case 3:
          final stats = await service.getAgeGroupStats(tableName);
          if (stats.isNotEmpty) successCount++;
          break;
      }
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    final result = QueryTestResult(
      testName: testName,
      duration: duration,
      successfulQueries: successCount,
      totalQueries: iterations,
    );

    _results[testName] = result;

    print(
        '    ✓ 聚合查询: ${result.queriesPerSecond.toStringAsFixed(2)} queries/s (${successCount}/${iterations} 成功)');
  }

  /// 多级别并发查询测试
  Future<void> _runMultiLevelConcurrentQueryTests(BackendService service, TestConfig config,
      int totalRecords, String tableName) async {
    print('  🔍 多级别并发查询测试...');

    for (final concurrency in config.queryConcurrencyLevels) {
      await _runSingleConcurrentQueryTest(service, config, totalRecords, tableName, concurrency);
    }
  }

  /// 单个并发级别的查询测试
  Future<void> _runSingleConcurrentQueryTest(BackendService service, TestConfig config,
      int totalRecords, String tableName, int concurrency) async {
    print('    🔍 ${concurrency}线程并发查询测试...');

    final testName = '${service.name}_concurrent_query_${concurrency}threads';
    final startTime = DateTime.now();

    int successCount = 0;
    final iterationsPerTask = (config.queryIterations ~/ concurrency).clamp(10, config.queryIterations ~/ concurrency);
    final totalIterations = concurrency * iterationsPerTask;

    // 创建并发任务
    final tasks = <Future<int>>[];

    for (int i = 0; i < concurrency; i++) {
      tasks.add(_runConcurrentQueryTask(service, tableName, iterationsPerTask, totalRecords, config));
    }

    // 等待所有并发任务完成
    final results = await Future.wait(tasks);
    successCount = results.fold(0, (sum, count) => sum + count);

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    final result = QueryTestResult(
      testName: testName,
      duration: duration,
      successfulQueries: successCount,
      totalQueries: totalIterations,
    );

    _results[testName] = result;

    print(
        '      ✓ ${concurrency}线程并发: ${result.queriesPerSecond.toStringAsFixed(2)} queries/s (${successCount}/${totalIterations} 成功)');
  }

  /// 单个并发查询任务
  Future<int> _runConcurrentQueryTask(BackendService service, String tableName,
      int iterations, int totalRecords, TestConfig config) async {
    int successCount = 0;
    int errorCount = 0;

    // 先获取一些实际存在的记录来获取真实的ID
    List<dynamic> sampleRecords = [];
    try {
      sampleRecords = await service.getRecordsPaginated(tableName, 0, 50);
    } catch (e) {
      // 如果无法获取样本记录，记录错误但继续测试
      errorCount++;
    }

    for (int i = 0; i < iterations; i++) {
      try {
        switch (i % 4) {
          case 0:
            // ID查询
            if (sampleRecords.isNotEmpty) {
              final randomRecord = sampleRecords[_random.nextInt(sampleRecords.length)];
              if (randomRecord.id != null) {
                final record = await service.getRecordById(tableName, randomRecord.id!)
                    .timeout(const Duration(seconds: 5));
                if (record != null) successCount++;
              }
            }
            break;
          case 1:
            // 条件查询
            final age = 25 + _random.nextInt(20);
            final records = await service.getRecordsByCondition(tableName, {'age': age}, limit: 50)
                .timeout(const Duration(seconds: 8));
            if (records.isNotEmpty) successCount++;
            break;
          case 2:
            // 分页查询
            final pageSize = 50;
            final maxOffset = totalRecords > pageSize ? totalRecords - pageSize : 0;
            final offset = maxOffset > 0 ? _random.nextInt(maxOffset) + 1 : 1;
            final records = await service.getRecordsPaginated(tableName, offset, pageSize)
                .timeout(const Duration(seconds: 5));
            if (records.isNotEmpty) successCount++;
            break;
          case 3:
            // 范围查询
            final minAge = 20 + _random.nextInt(20);
            final maxAge = minAge + 10;
            final records = await service.getRecordsByAgeRange(tableName, minAge, maxAge, limit: 50)
                .timeout(const Duration(seconds: 8));
            if (records.isNotEmpty) successCount++;
            break;
        }
      } catch (e) {
        errorCount++;
        // 记录错误类型但继续测试
        if (errorCount > iterations * 0.5) {
          // 如果错误率超过50%，提前退出
          break;
        }
      }
    }

    return successCount;
  }
}
