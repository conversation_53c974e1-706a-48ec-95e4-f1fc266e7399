import 'dart:async';
import 'dart:convert';
import 'dart:math';
// import 'package:web_socket_channel/web_socket_channel.dart';
import '../config/vps_stress_test_config.dart';
import '../utils/progressive_load_controller.dart';
import '../utils/real_time_monitor.dart';

/// 虚拟WebSocket连接
class VirtualWebSocketConnection {
  final int connectionId;
  final String serverUrl;
  final VpsStressTestConfig config;
  final RealTimeMonitor monitor;

  // WebSocketChannel? _channel;
  dynamic _channel; // 临时替代，等待依赖安装
  bool _isConnected = false;
  bool _isActive = false;
  Timer? _heartbeatTimer;
  Timer? _messageTimer;
  int _messagesSent = 0;
  int _messagesReceived = 0;
  int _errorsCount = 0;
  DateTime? _connectionStartTime;
  final Random _random = Random();

  VirtualWebSocketConnection({
    required this.connectionId,
    required this.serverUrl,
    required this.config,
    required this.monitor,
  });

  /// 建立连接
  Future<bool> connect() async {
    if (_isConnected) return true;

    try {
      _connectionStartTime = DateTime.now();
      final wsUrl = serverUrl.replaceFirst('http', 'ws') + '/api/realtime';

      // _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      // 临时模拟连接成功，等待依赖安装后实现
      _channel = 'mock_connection';

      // 监听消息
      _channel!.stream.listen(
        (message) {
          _messagesReceived++;
          _handleMessage(message);
        },
        onError: (error) {
          _errorsCount++;
          monitor.recordRequest(
            RequestResult(
              success: false,
              responseTimeMs: 0,
              timestamp: DateTime.now(),
              operation: 'websocket_error',
              error: error.toString(),
            ),
          );
        },
        onDone: () {
          _isConnected = false;
          _cleanup();
        },
      );

      _isConnected = true;
      _isActive = true;

      // 记录连接成功
      final connectionTime = DateTime.now()
          .difference(_connectionStartTime!)
          .inMilliseconds;
      monitor.recordRequest(
        RequestResult(
          success: true,
          responseTimeMs: connectionTime,
          timestamp: DateTime.now(),
          operation: 'websocket_connect',
        ),
      );

      // 启动心跳和消息发送
      _startHeartbeat();
      _startMessageSending();

      return true;
    } catch (e) {
      _errorsCount++;
      monitor.recordRequest(
        RequestResult(
          success: false,
          responseTimeMs: config.connectionTimeoutSeconds * 1000,
          timestamp: DateTime.now(),
          operation: 'websocket_connect',
          error: e.toString(),
        ),
      );
      return false;
    }
  }

  /// 断开连接
  void disconnect() {
    _isActive = false;
    _cleanup();
  }

  /// 清理资源
  void _cleanup() {
    _heartbeatTimer?.cancel();
    _messageTimer?.cancel();
    _channel?.sink.close();
    _heartbeatTimer = null;
    _messageTimer = null;
    _channel = null;
    _isConnected = false;
  }

  /// 启动心跳
  void _startHeartbeat() {
    if (!_isActive) return;

    _heartbeatTimer = Timer.periodic(
      Duration(seconds: config.heartbeatIntervalSeconds),
      (timer) {
        if (_isActive && _isConnected) {
          _sendHeartbeat();
        }
      },
    );
  }

  /// 启动消息发送
  void _startMessageSending() {
    if (!_isActive) return;

    _scheduleNextMessage();
  }

  /// 调度下一条消息
  void _scheduleNextMessage() {
    if (!_isActive) return;

    final interval = config.getRandomMessageFrequency();
    _messageTimer = Timer(Duration(seconds: interval), () {
      if (_isActive && _isConnected) {
        _sendMessage();
        _scheduleNextMessage();
      }
    });
  }

  /// 发送心跳
  void _sendHeartbeat() {
    if (!_isConnected || _channel == null) return;

    try {
      final heartbeat = json.encode({
        'type': 'heartbeat',
        'timestamp': DateTime.now().toIso8601String(),
        'connection_id': connectionId,
      });

      _channel!.sink.add(heartbeat);

      monitor.recordRequest(
        RequestResult(
          success: true,
          responseTimeMs: 0,
          timestamp: DateTime.now(),
          operation: 'websocket_heartbeat',
        ),
      );
    } catch (e) {
      _errorsCount++;
      monitor.recordRequest(
        RequestResult(
          success: false,
          responseTimeMs: 0,
          timestamp: DateTime.now(),
          operation: 'websocket_heartbeat',
          error: e.toString(),
        ),
      );
    }
  }

  /// 发送消息
  void _sendMessage() {
    if (!_isConnected || _channel == null) return;

    try {
      final messageData = _generateRandomMessage();
      final message = json.encode(messageData);

      final startTime = DateTime.now();
      _channel!.sink.add(message);
      _messagesSent++;

      monitor.recordRequest(
        RequestResult(
          success: true,
          responseTimeMs: DateTime.now().difference(startTime).inMilliseconds,
          timestamp: DateTime.now(),
          operation: 'websocket_send',
        ),
      );
    } catch (e) {
      _errorsCount++;
      monitor.recordRequest(
        RequestResult(
          success: false,
          responseTimeMs: 0,
          timestamp: DateTime.now(),
          operation: 'websocket_send',
          error: e.toString(),
        ),
      );
    }
  }

  /// 生成随机消息
  Map<String, dynamic> _generateRandomMessage() {
    final messageTypes = ['chat', 'notification', 'update', 'ping'];
    final messageType = messageTypes[_random.nextInt(messageTypes.length)];

    // 生成指定大小的消息内容
    final contentSize = config.messageSizeBytes - 100; // 预留给其他字段
    final content = List.generate(contentSize, (i) => 'a').join();

    return {
      'type': messageType,
      'connection_id': connectionId,
      'timestamp': DateTime.now().toIso8601String(),
      'content': content,
      'sequence': _messagesSent + 1,
    };
  }

  /// 处理接收到的消息
  void _handleMessage(dynamic message) {
    try {
      // 简单处理消息，记录接收成功
      monitor.recordRequest(
        RequestResult(
          success: true,
          responseTimeMs: 0,
          timestamp: DateTime.now(),
          operation: 'websocket_receive',
        ),
      );
    } catch (e) {
      _errorsCount++;
      monitor.recordRequest(
        RequestResult(
          success: false,
          responseTimeMs: 0,
          timestamp: DateTime.now(),
          operation: 'websocket_receive',
          error: e.toString(),
        ),
      );
    }
  }

  /// 获取连接统计信息
  Map<String, dynamic> getStats() {
    final uptime = _connectionStartTime != null
        ? DateTime.now().difference(_connectionStartTime!).inSeconds
        : 0;

    return {
      'connection_id': connectionId,
      'is_connected': _isConnected,
      'is_active': _isActive,
      'uptime_seconds': uptime,
      'messages_sent': _messagesSent,
      'messages_received': _messagesReceived,
      'errors_count': _errorsCount,
    };
  }
}

/// VPS WebSocket压力测试
class VpsWebSocketStressTest {
  final VpsStressTestConfig config;
  final String serverUrl;

  late final RealTimeMonitor _monitor;
  late final ProgressiveLoadController _loadController;

  final List<VirtualWebSocketConnection> _connections = [];
  bool _isRunning = false;
  DateTime? _testStartTime;
  DateTime? _testEndTime;

  // 回调函数
  Function(String message)? onStatusUpdate;
  Function(PerformanceMetrics metrics)? onMetricsUpdate;
  Function(String alert)? onAlert;

  VpsWebSocketStressTest({required this.config, required this.serverUrl}) {
    _initializeMonitor();
    _initializeLoadController();
  }

  /// 初始化监控器
  void _initializeMonitor() {
    _monitor = RealTimeMonitor(
      metricsCollectionIntervalSeconds: config.metricsCollectionIntervalSeconds,
      performanceCheckIntervalSeconds: config.performanceCheckIntervalSeconds,
      successRateThreshold: config.successRateThreshold,
      responseTimeThresholdMs: config.responseTimeThresholdMs,
    );

    _monitor.onMetricsCollected = (metrics) {
      // 更新连接数信息
      final activeConnections = _connections
          .where((c) => c._isConnected)
          .length;
      final updatedMetrics = PerformanceMetrics(
        currentUsers: activeConnections,
        successRate: metrics.successRate,
        averageResponseTime: metrics.averageResponseTime,
        p95ResponseTime: metrics.p95ResponseTime,
        timeoutCount: metrics.timeoutCount,
        errorCount: metrics.errorCount,
        totalRequests: metrics.totalRequests,
        timestamp: metrics.timestamp,
      );

      _loadController.updateMetrics(updatedMetrics);
      onMetricsUpdate?.call(updatedMetrics);
    };

    _monitor.onPerformanceAlert = (alert) {
      onAlert?.call(alert);
    };
  }

  /// 初始化负载控制器
  void _initializeLoadController() {
    _loadController = ProgressiveLoadController(
      initialUsers: config.websocketInitialConnections,
      maxUsers: config.websocketMaxConnections,
      baseIncrementStep: config.websocketIncrementStep,
      incrementIntervalSeconds: config.websocketIncrementIntervalSeconds,
      successRateThreshold: config.successRateThreshold,
      responseTimeThresholdMs: config.responseTimeThresholdMs,
      failureThresholdConsecutive: config.failureThresholdConsecutive,
    );

    _loadController.onUserCountChanged = (newConnectionCount) {
      _adjustConnectionCount(newConnectionCount);
    };

    _loadController.onTestStopped = (reason) {
      onStatusUpdate?.call('测试停止: $reason');
      _stopTest();
    };
  }

  /// 开始WebSocket压力测试
  Future<void> startTest() async {
    if (_isRunning) {
      print('WebSocket压力测试已在运行中');
      return;
    }

    print('🔗 开始VPS WebSocket压力测试');
    print('   服务器: $serverUrl');

    _isRunning = true;
    _testStartTime = DateTime.now();

    onStatusUpdate?.call('WebSocket压力测试已开始');

    // 启动监控
    _monitor.startMonitoring();

    // 启动渐进式负载
    _loadController.startProgressiveLoad();

    // 等待测试完成或超时
    await _waitForTestCompletion();
  }

  /// 停止测试
  void _stopTest() {
    if (!_isRunning) return;

    _isRunning = false;
    _testEndTime = DateTime.now();

    // 断开所有连接
    for (final connection in _connections) {
      connection.disconnect();
    }

    // 停止监控和负载控制
    _monitor.stopMonitoring();
    _loadController.stopProgressiveLoad();

    onStatusUpdate?.call('WebSocket压力测试已完成');

    print('✅ VPS WebSocket压力测试完成');
    _printTestSummary();
  }

  /// 调整连接数量
  Future<void> _adjustConnectionCount(int targetConnectionCount) async {
    final currentActiveConnections = _connections
        .where((c) => c._isConnected)
        .length;

    if (targetConnectionCount > currentActiveConnections) {
      // 增加连接
      final connectionsToAdd = targetConnectionCount - currentActiveConnections;
      for (int i = 0; i < connectionsToAdd; i++) {
        final connectionId = _connections.length + 1;
        final connection = VirtualWebSocketConnection(
          connectionId: connectionId,
          serverUrl: serverUrl,
          config: config,
          monitor: _monitor,
        );

        _connections.add(connection);

        // 异步建立连接，避免阻塞
        connection.connect().then((success) {
          if (!success) {
            print('⚠️ 连接 $connectionId 建立失败');
          }
        });

        // 添加小延迟避免同时建立太多连接
        await Future.delayed(const Duration(milliseconds: 10));
      }

      onStatusUpdate?.call(
        '增加WebSocket连接: $currentActiveConnections -> $targetConnectionCount',
      );
    } else if (targetConnectionCount < currentActiveConnections) {
      // 减少连接
      final connectionsToStop =
          currentActiveConnections - targetConnectionCount;
      int stopped = 0;

      for (final connection in _connections.reversed) {
        if (connection._isConnected && stopped < connectionsToStop) {
          connection.disconnect();
          stopped++;
        }
      }

      onStatusUpdate?.call(
        '减少WebSocket连接: $currentActiveConnections -> $targetConnectionCount',
      );
    }
  }

  /// 等待测试完成
  Future<void> _waitForTestCompletion() async {
    final testDuration = Duration(minutes: config.testDurationMinutes);
    final endTime = _testStartTime!.add(testDuration);

    while (_isRunning && DateTime.now().isBefore(endTime)) {
      await Future.delayed(const Duration(seconds: 1));
    }

    if (_isRunning) {
      onStatusUpdate?.call('测试时间到达，正在停止测试');
      _stopTest();
    }
  }

  /// 打印测试摘要
  void _printTestSummary() {
    if (_testStartTime == null) return;

    final duration = (_testEndTime ?? DateTime.now()).difference(
      _testStartTime!,
    );
    final stats = _monitor.getCurrentStats();

    print('\n=== VPS WebSocket压力测试摘要 ===');
    print('服务器: $serverUrl');
    print('测试持续时间: ${duration.inMinutes}分${duration.inSeconds % 60}秒');
    print('最大并发连接数: ${_loadController.maxStableUsers}');
    print('总请求数: ${stats['total_requests']}');
    print(
      '成功率: ${((stats['success_rate'] as double) * 100).toStringAsFixed(1)}%',
    );
    print(
      '平均响应时间: ${(stats['average_response_time'] as double).toStringAsFixed(0)}ms',
    );

    final recommendation = _loadController.getRecommendation();
    print('建议: $recommendation');
    print('');
  }

  /// 获取测试结果
  Map<String, dynamic> getTestResults() {
    final duration = _testStartTime != null
        ? (_testEndTime ?? DateTime.now()).difference(_testStartTime!)
        : Duration.zero;

    final stats = _monitor.getCurrentStats();
    final operationStats = _monitor.getOperationStats();
    final recentErrors = _monitor.getRecentErrors();

    return {
      'test_type': 'websocket_stress_test',
      'server_url': serverUrl,
      'test_duration_seconds': duration.inSeconds,
      'max_stable_connections': _loadController.maxStableUsers,
      'has_reached_limit': _loadController.hasReachedLimit,
      'performance_stats': stats,
      'operation_stats': operationStats,
      'recent_errors': recentErrors,
      'recommendation': _loadController.getRecommendation(),
      'performance_trend': _loadController.getPerformanceTrend(),
      'connection_stats': _connections.map((c) => c.getStats()).toList(),
      'test_config': {
        'initial_connections': config.websocketInitialConnections,
        'max_connections': config.websocketMaxConnections,
        'increment_step': config.websocketIncrementStep,
        'message_size_bytes': config.messageSizeBytes,
        'heartbeat_interval': config.heartbeatIntervalSeconds,
      },
    };
  }

  /// 强制停止测试
  void forceStop() {
    onStatusUpdate?.call('强制停止WebSocket压力测试');
    _stopTest();
  }
}
