import 'dart:async';
import '../models/test_record.dart';
import '../services/backend_service.dart';
import '../utils/data_generator.dart';
import '../utils/performance_tracker.dart';
import '../config/test_config.dart';

class PerformanceTest {
  final List<BackendService> _services = [];
  final List<PerformanceResult> _results = [];
  final String _tableName;

  PerformanceTest() : _tableName = 'performance_test';

  /// 添加要测试的后端服务
  void addService(BackendService service) {
    _services.add(service);
  }

  /// 运行完整的性能测试套件
  Future<void> runFullTestSuite({
    int totalRecords = 100000,
    int batchSize = 1000,
    List<int> concurrentLevels = const [1, 2, 5, 10],
    int intervalSeconds = 5,
  }) async {
    print('=== 开始后端性能测试套件 ===');
    print('总记录数: $totalRecords');
    print('批量大小: $batchSize');
    print('并发级别: $concurrentLevels');
    print('测试表名: $_tableName');
    print('');

    // 生成测试数据
    print('生成测试数据...');
    final testRecords = DataGenerator.generateRecords(totalRecords);
    final batches = DataGenerator.batchRecords(testRecords, batchSize);
    print('');

    // 对每个后端服务运行测试
    for (int i = 0; i < _services.length; i++) {
      final service = _services[i];
      await _runServiceTests(
        service,
        testRecords,
        batches,
        concurrentLevels,
        null,
      );

      // 如果不是最后一个服务，等待间隔时间
      if (i < _services.length - 1) {
        print('\n⏳ 等待 ${intervalSeconds}秒 让系统资源释放...');
        await Future.delayed(Duration(seconds: intervalSeconds));
        print('✓ 等待完成，继续下一个后端测试\n');
      } else {
        print('');
      }
    }

    // 不再生成单独的插入报告，统一在main中生成
  }

  /// 运行完整的性能测试套件并返回结果
  Future<List<PerformanceResult>> runFullTestSuiteWithResults({
    int totalRecords = 100000,
    int batchSize = 1000,
    List<int> concurrentLevels = const [1, 2, 5, 10],
    int intervalSeconds = 5,
    TestConfig? config,
  }) async {
    print('=== 开始后端性能测试套件 ===');
    print('总记录数: $totalRecords');
    print('批量大小: $batchSize');
    print('并发级别: $concurrentLevels');
    print('测试表名: $_tableName');
    print('');

    // 生成测试数据
    print('生成测试数据...');
    final testRecords = DataGenerator.generateRecords(totalRecords);
    final batches = DataGenerator.batchRecords(testRecords, batchSize);
    print('');

    // 对每个后端服务运行测试
    for (int i = 0; i < _services.length; i++) {
      final service = _services[i];
      await _runServiceTests(
        service,
        testRecords,
        batches,
        concurrentLevels,
        config,
      );

      // 如果不是最后一个服务，等待间隔时间
      if (i < _services.length - 1) {
        print('\n⏳ 等待 ${intervalSeconds}秒 让系统资源释放...');
        await Future.delayed(Duration(seconds: intervalSeconds));
        print('✓ 等待完成，继续下一个后端测试\n');
      } else {
        print('');
      }
    }

    return List.from(_results);
  }

  /// 对单个服务运行所有测试
  Future<void> _runServiceTests(
    BackendService service,
    List<TestRecord> testRecords,
    List<List<TestRecord>> batches,
    List<int> concurrentLevels,
    TestConfig? config,
  ) async {
    print('=== 测试 ${service.name} ===');

    // 初始化服务
    if (!await service.initialize()) {
      print('✗ ${service.name} 初始化失败，跳过测试');
      return;
    }

    // 创建测试表
    if (!await service.createTestTable(_tableName)) {
      print('✗ ${service.name} 创建测试表失败，跳过测试');
      return;
    }

    try {
      // 1. 单条插入测试
      if (config?.enableSingleInsert ?? true) {
        await _runSingleInsertTest(service, testRecords.take(1000).toList());
      } else {
        print('⏭️ 跳过单条插入测试（已禁用）');
      }

      // 2. 批量插入测试
      if (config?.enableBatchInsert ?? true) {
        await _runBatchInsertTest(service, batches.take(10).toList());
      } else {
        print('⏭️ 跳过批量插入测试（已禁用）');
      }

      // 3. 并发插入测试
      if (config?.enableConcurrentInsert ?? true) {
        for (final concurrency in concurrentLevels) {
          await _runConcurrentInsertTest(
            service,
            batches, // 使用所有批次数据，确保测试数据量一致
            concurrency,
          );
        }
      } else {
        print('⏭️ 跳过并发插入测试（已禁用）');
      }
    } finally {
      // 保留数据用于查询测试
      print('🔄 ${service.name} 数据已保留，用于后续查询性能测试');
      await service.close();
    }
  }

  /// 单条插入测试
  Future<void> _runSingleInsertTest(
    BackendService service,
    List<TestRecord> records,
  ) async {
    final tracker = PerformanceTracker(backend: service.name, testType: '单条插入');

    tracker.startTest(records.length);

    final startTime = DateTime.now();
    int successCount = 0;
    int failCount = 0;
    final errors = <String>[];

    for (int i = 0; i < records.length; i++) {
      try {
        final result = await service.insertRecord(_tableName, records[i]);

        if (result != null) {
          successCount++;
        } else {
          failCount++;
          errors.add('记录 $i 插入失败');
        }

        // 每100条记录显示进度（但不重复计数）
        if ((i + 1) % 100 == 0) {
          final progress = ((i + 1) / records.length * 100).toStringAsFixed(1);
          print(
            '单条插入进度: ${i + 1}/${records.length} ($progress%) - 成功: $successCount, 失败: $failCount',
          );
        }
      } catch (e) {
        failCount++;
        errors.add('记录 $i 插入异常: $e');
      }
    }

    final endTime = DateTime.now();
    final totalDuration = endTime.difference(startTime).inMilliseconds / 1000.0;

    // 只在最后调用一次recordBatch，避免重复计数
    tracker.recordBatch(
      batchSize: records.length,
      successCount: successCount,
      failCount: failCount,
      batchTimeSeconds: totalDuration,
      batchErrors: errors,
    );

    final result = tracker.finishTest();
    _results.add(result);
  }

  /// 批量插入测试
  Future<void> _runBatchInsertTest(
    BackendService service,
    List<List<TestRecord>> batches,
  ) async {
    final totalRecords = batches.fold<int>(
      0,
      (sum, batch) => sum + batch.length,
    );

    final tracker = PerformanceTracker(backend: service.name, testType: '批量插入');

    tracker.startTest(totalRecords);

    for (int i = 0; i < batches.length; i++) {
      final batch = batches[i];
      final batchStartTime = DateTime.now();

      try {
        final results = await service.insertRecords(_tableName, batch);
        final batchEndTime = DateTime.now();
        final batchDuration =
            batchEndTime.difference(batchStartTime).inMilliseconds / 1000.0;

        final successCount = results.length;
        final failCount = batch.length - successCount;

        tracker.recordBatch(
          batchSize: batch.length,
          successCount: successCount,
          failCount: failCount,
          batchTimeSeconds: batchDuration,
        );
      } catch (e) {
        final batchEndTime = DateTime.now();
        final batchDuration =
            batchEndTime.difference(batchStartTime).inMilliseconds / 1000.0;

        tracker.recordBatch(
          batchSize: batch.length,
          successCount: 0,
          failCount: batch.length,
          batchTimeSeconds: batchDuration,
          batchErrors: ['批次 $i 插入异常: $e'],
        );
      }
    }

    final result = tracker.finishTest();
    _results.add(result);
  }

  /// 并发插入测试
  Future<void> _runConcurrentInsertTest(
    BackendService service,
    List<List<TestRecord>> batches,
    int concurrency,
  ) async {
    final totalRecords = batches.fold<int>(
      0,
      (sum, batch) => sum + batch.length,
    );

    final tracker = PerformanceTracker(
      backend: service.name,
      testType: '并发插入_${concurrency}线程',
    );

    tracker.startTest(totalRecords);

    // 将批次分配给不同的并发任务
    final tasks = <Future<void>>[];
    final batchesPerTask = (batches.length / concurrency).ceil();

    for (int i = 0; i < concurrency; i++) {
      final startIndex = i * batchesPerTask;
      final endIndex = ((i + 1) * batchesPerTask).clamp(0, batches.length);

      if (startIndex < batches.length) {
        final taskBatches = batches.sublist(startIndex, endIndex);
        tasks.add(_runConcurrentTask(service, taskBatches, tracker, i));
      }
    }

    // 等待所有并发任务完成
    await Future.wait(tasks);

    final result = tracker.finishTest();
    _results.add(result);
  }

  /// 单个并发任务
  Future<void> _runConcurrentTask(
    BackendService service,
    List<List<TestRecord>> batches,
    PerformanceTracker tracker,
    int taskId,
  ) async {
    for (int i = 0; i < batches.length; i++) {
      final batch = batches[i];
      final batchStartTime = DateTime.now();

      try {
        final results = await service.insertRecords(_tableName, batch);
        final batchEndTime = DateTime.now();
        final batchDuration =
            batchEndTime.difference(batchStartTime).inMilliseconds / 1000.0;

        final successCount = results.length;
        final failCount = batch.length - successCount;

        tracker.recordBatch(
          batchSize: batch.length,
          successCount: successCount,
          failCount: failCount,
          batchTimeSeconds: batchDuration,
        );
      } catch (e) {
        final batchEndTime = DateTime.now();
        final batchDuration =
            batchEndTime.difference(batchStartTime).inMilliseconds / 1000.0;

        tracker.recordBatch(
          batchSize: batch.length,
          successCount: 0,
          failCount: batch.length,
          batchTimeSeconds: batchDuration,
          batchErrors: ['任务 $taskId 批次 $i 异常: $e'],
        );
      }
    }
  }

  /// 获取测试结果
  List<PerformanceResult> get results => List.unmodifiable(_results);
}
