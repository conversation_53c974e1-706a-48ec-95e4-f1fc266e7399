import 'dart:async';
import 'dart:io';
import '../config/vps_stress_test_config.dart';
import '../services/backend_service.dart';
import '../services/pocketbase_service.dart';
import '../services/trailbase_service_simplified.dart';
import '../utils/data_generator.dart';
import '../utils/vps_stress_report_generator.dart';
import 'vps_write_stress_test.dart';
import 'vps_read_stress_test.dart';
// import 'vps_websocket_stress_test_simple.dart';

/// VPS压力测试运行器
class VpsStressTestRunner {
  final VpsStressTestConfig config;
  late final BackendService _service;

  VpsWriteStressTest? _writeTest;
  VpsReadStressTest? _readTest;
  // VpsWebSocketStressTest? _websocketTest; // 暂时禁用

  Map<String, dynamic>? _writeResults;
  Map<String, dynamic>? _readResults;
  Map<String, dynamic>? _websocketResults;

  bool _isRunning = false;
  DateTime? _testStartTime;
  DateTime? _testEndTime;

  VpsStressTestRunner({required this.config}) {
    _initializeService();
  }

  /// 初始化服务
  void _initializeService() {
    switch (config.serverType.toLowerCase()) {
      case 'pocketbase':
        _service = PocketBaseService(config.serverUrl);
        break;
      case 'trailbase':
        _service = TrailBaseServiceSimplified(config.serverUrl);
        break;
      default:
        throw ArgumentError('不支持的服务器类型: ${config.serverType}');
    }
  }

  /// 运行完整的VPS压力测试
  Future<void> runCompleteStressTest() async {
    if (_isRunning) {
      print('VPS压力测试已在运行中');
      return;
    }

    print('🚀 开始完整的VPS压力测试');
    print('   服务器: ${config.serverName} (${config.serverType})');
    print('   URL: ${config.serverUrl}');
    print('   测试项目: ${_getEnabledTests().join(', ')}');
    print('');

    _isRunning = true;
    _testStartTime = DateTime.now();

    try {
      // 初始化服务
      if (!await _service.initialize()) {
        throw Exception('服务初始化失败');
      }

      // 准备测试数据
      await _prepareTestData();

      // 运行各项测试
      if (config.enableWriteStressTest) {
        await _runWriteStressTest();
      }

      if (config.enableReadStressTest) {
        await _runReadStressTest();
      }

      if (config.enableWebsocketStressTest) {
        await _runWebSocketStressTest();
      }

      // 生成报告
      await _generateReports();
    } catch (e) {
      print('❌ VPS压力测试过程中出现错误: $e');
    } finally {
      _isRunning = false;
      _testEndTime = DateTime.now();
      _printFinalSummary();
    }
  }

  /// 获取启用的测试项目
  List<String> _getEnabledTests() {
    final tests = <String>[];
    if (config.enableWriteStressTest) tests.add('写入压力测试');
    if (config.enableReadStressTest) tests.add('读取压力测试');
    if (config.enableWebsocketStressTest) tests.add('WebSocket压力测试');
    return tests;
  }

  /// 准备测试数据
  Future<void> _prepareTestData() async {
    print('📋 准备测试数据...');

    final tableName = config.testTableName;

    // 创建测试表
    if (!await _service.createTestTable(tableName)) {
      throw Exception('创建测试表失败');
    }

    // 为读取测试准备一些初始数据
    if (config.enableReadStressTest) {
      if (config.initialDataCount >= 1000) {
        print('   📊 为读取压力测试准备初始数据 (${config.initialDataCount}条记录)...');
        print('   💡 说明: 读取测试需要预先在数据库中插入数据才能进行查询操作');
      }
      final initialRecords = DataGenerator.generateRecords(
        config.initialDataCount,
      );

      // 批量插入初始数据到VPS服务器数据库
      const batchSize = 100;
      for (int i = 0; i < initialRecords.length; i += batchSize) {
        final batch = initialRecords.skip(i).take(batchSize).toList();
        await _service.insertRecords(tableName, batch);

        // 只在大量数据时显示进度
        if (initialRecords.length >= 1000) {
          final progress = ((i + batch.length) / initialRecords.length * 100)
              .toStringAsFixed(1);
          stdout.write('\r   📤 插入到VPS数据库进度: $progress%');
        }
      }
      if (initialRecords.length >= 1000) {
        print('\n   ✅ 初始数据已成功插入到VPS服务器数据库 (${initialRecords.length}条记录)');
      }
    }

    print('✅ 测试数据准备完成');
    print('');
  }

  /// 运行写入压力测试
  Future<void> _runWriteStressTest() async {
    print('🚀 开始写入压力测试...');

    _writeTest = VpsWriteStressTest(
      config: config,
      service: _service,
      tableName: config.testTableName,
    );

    // 设置回调
    _writeTest!.onStatusUpdate = (message) {
      print('📝 写入测试: $message');
    };

    _writeTest!.onMetricsUpdate = (metrics) {
      _printMetricsUpdate('写入', metrics);
    };

    _writeTest!.onAlert = (alert) {
      print('⚠️ 写入测试警告: $alert');
    };

    // 运行测试
    await _writeTest!.startTest();

    // 获取结果
    _writeResults = _writeTest!.getTestResults();

    print('✅ 写入压力测试完成');
    print('');
  }

  /// 运行读取压力测试
  Future<void> _runReadStressTest() async {
    print('🔍 开始读取压力测试...');

    _readTest = VpsReadStressTest(
      config: config,
      service: _service,
      tableName: config.testTableName,
    );

    // 设置回调
    _readTest!.onStatusUpdate = (message) {
      print('📖 读取测试: $message');
    };

    _readTest!.onMetricsUpdate = (metrics) {
      _printMetricsUpdate('读取', metrics);
    };

    _readTest!.onAlert = (alert) {
      print('⚠️ 读取测试警告: $alert');
    };

    // 运行测试
    await _readTest!.startTest();

    // 获取结果
    _readResults = _readTest!.getTestResults();

    print('✅ 读取压力测试完成');
    print('');
  }

  /// 运行WebSocket压力测试（暂时禁用）
  Future<void> _runWebSocketStressTest() async {
    print('🔗 WebSocket压力测试暂时禁用，等待依赖安装...');

    _websocketResults = {
      'status': 'disabled',
      'message': '等待web_socket_channel依赖安装后启用',
      'max_connections_achieved': 0,
      'test_duration_minutes': 0,
    };

    print('⚠️ WebSocket压力测试跳过');
    print('');
  }

  // 用于跟踪上次输出的指标
  int _lastUsers = 0;
  double _lastSuccessRate = 100.0;
  double _lastResponseTime = 0.0;

  /// 打印指标更新（精简模式）
  void _printMetricsUpdate(String testType, dynamic metrics) {
    final currentUsers = metrics.currentUsers;
    final successRateValue = (metrics.successRate * 100);
    final avgResponseTimeValue = metrics.averageResponseTime;
    final successRate = successRateValue.toStringAsFixed(1);
    final avgResponseTime = avgResponseTimeValue.toStringAsFixed(0);

    // 只在用户数变化或性能显著变化时打印
    final userChanged = currentUsers != _lastUsers;
    final performanceChanged =
        (successRateValue - _lastSuccessRate).abs() > 5.0 ||
        (avgResponseTimeValue - _lastResponseTime).abs() > 500.0;

    if (userChanged || performanceChanged) {
      print(
        '📊 $testType - 用户数: $currentUsers, 成功率: $successRate%, 响应时间: ${avgResponseTime}ms',
      );

      _lastUsers = currentUsers;
      _lastSuccessRate = successRateValue;
      _lastResponseTime = avgResponseTimeValue;
    }
  }

  /// 生成报告
  Future<void> _generateReports() async {
    print('📄 生成测试报告...');

    await VpsStressReportGenerator.generateCompleteReport(
      writeTestResults: _writeResults,
      readTestResults: _readResults,
      websocketTestResults: _websocketResults,
      serverName: config.serverName,
      generateMarkdown: config.generateMarkdownReport,
      generateJson: config.generateJsonReport,
      generateCsv: config.generateCsvReport,
    );

    print('✅ 测试报告生成完成');
    print('');
  }

  /// 打印最终摘要
  void _printFinalSummary() {
    if (_testStartTime == null) return;

    final duration = (_testEndTime ?? DateTime.now()).difference(
      _testStartTime!,
    );

    print('');
    print('=' * 60);
    print('🎯 VPS压力测试完成摘要');
    print('=' * 60);
    print('服务器: ${config.serverName} (${config.serverType})');
    print('总测试时间: ${duration.inMinutes}分${duration.inSeconds % 60}秒');
    print('');

    if (_writeResults != null) {
      final maxUsers = _writeResults!['max_stable_users'] ?? 0;
      final hasReachedLimit = _writeResults!['has_reached_limit'] ?? false;
      print('📝 写入极限: $maxUsers 并发用户 ${hasReachedLimit ? "(已达极限)" : "(可能更高)"}');
    }

    if (_readResults != null) {
      final maxUsers = _readResults!['max_stable_users'] ?? 0;
      final hasReachedLimit = _readResults!['has_reached_limit'] ?? false;
      print('📖 读取极限: $maxUsers 并发用户 ${hasReachedLimit ? "(已达极限)" : "(可能更高)"}');
    }

    if (_websocketResults != null) {
      final maxConnections = _websocketResults!['max_stable_connections'] ?? 0;
      final hasReachedLimit = _websocketResults!['has_reached_limit'] ?? false;
      print(
        '🌐 WebSocket极限: $maxConnections 并发连接 ${hasReachedLimit ? "(已达极限)" : "(可能更高)"}',
      );
    }

    print('');
    print('📊 详细报告已生成，请查看相应的报告文件');
    print('=' * 60);
  }

  /// 运行单项测试
  Future<void> runSingleTest(String testType) async {
    switch (testType.toLowerCase()) {
      case 'write':
        if (!await _service.initialize()) {
          throw Exception('服务初始化失败');
        }
        await _prepareTestData();
        await _runWriteStressTest();
        break;

      case 'read':
        if (!await _service.initialize()) {
          throw Exception('服务初始化失败');
        }
        await _prepareTestData();
        await _runReadStressTest();
        break;

      case 'websocket':
        await _runWebSocketStressTest();
        break;

      default:
        throw ArgumentError('不支持的测试类型: $testType');
    }

    await _generateReports();
    _printFinalSummary();
  }

  /// 停止所有测试
  void stopAllTests() {
    print('🛑 停止所有VPS压力测试...');

    _writeTest?.forceStop();
    _readTest?.forceStop();
    // _websocketTest?.forceStop(); // 暂时禁用

    _isRunning = false;
    print('✅ 所有测试已停止');
  }

  /// 获取测试状态
  Map<String, dynamic> getTestStatus() {
    return {
      'is_running': _isRunning,
      'test_start_time': _testStartTime?.toIso8601String(),
      'test_end_time': _testEndTime?.toIso8601String(),
      'enabled_tests': _getEnabledTests(),
      'write_test_completed': _writeResults != null,
      'read_test_completed': _readResults != null,
      'websocket_test_completed': _websocketResults != null,
    };
  }

  /// 获取所有测试结果
  Map<String, dynamic> getAllResults() {
    return {
      'write_results': _writeResults,
      'read_results': _readResults,
      'websocket_results': _websocketResults,
      'test_status': getTestStatus(),
    };
  }
}
