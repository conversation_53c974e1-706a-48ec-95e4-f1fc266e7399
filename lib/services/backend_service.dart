import '../models/test_record.dart';

/// 抽象的后端服务接口
abstract class BackendService {
  String get name;
  String get baseUrl;

  /// 初始化服务连接
  Future<bool> initialize();

  /// 测试连接
  Future<bool> testConnection();

  /// 创建测试表
  Future<bool> createTestTable(String tableName);

  /// 清空测试表
  Future<bool> clearTestTable(String tableName);

  /// 插入单条记录
  Future<TestRecord?> insertRecord(String tableName, TestRecord record);

  /// 批量插入记录
  Future<List<TestRecord>> insertRecords(
    String tableName,
    List<TestRecord> records,
  );

  /// 获取记录数量
  Future<int> getRecordCount(String tableName);

  /// 删除测试表
  Future<bool> deleteTestTable(String tableName);

  /// 关闭连接
  Future<void> close();

  /// 通用查询方法（用于VPS压力测试）
  Future<List<Map<String, dynamic>>?> queryRecords(
    String tableName,
    String filter, {
    int? page,
    int? perPage,
  });

  // ========== 查询接口 ==========

  /// 按ID查询单条记录
  Future<TestRecord?> getRecordById(String tableName, String id);

  /// 按条件查询记录列表
  Future<List<TestRecord>> getRecordsByCondition(
    String tableName,
    Map<String, dynamic> conditions, {
    int? limit,
  });

  /// 按年龄范围查询记录
  Future<List<TestRecord>> getRecordsByAgeRange(
    String tableName,
    int minAge,
    int maxAge, {
    int? limit,
  });

  /// 按分数范围查询记录
  Future<List<TestRecord>> getRecordsByScoreRange(
    String tableName,
    double minScore,
    double maxScore, {
    int? limit,
  });

  /// 分页查询记录
  Future<List<TestRecord>> getRecordsPaginated(
    String tableName,
    int offset,
    int limit,
  );

  /// 获取记录总数
  Future<int> getTotalRecordCount(String tableName);

  /// 按条件获取记录数量
  Future<int> getRecordCountByCondition(
    String tableName,
    Map<String, dynamic> conditions,
  );

  /// 获取平均分数
  Future<double?> getAverageScore(String tableName);

  /// 获取最高分数
  Future<double?> getMaxScore(String tableName);

  /// 获取最低分数
  Future<double?> getMinScore(String tableName);

  /// 按年龄分组统计
  Future<Map<int, int>> getAgeGroupStats(String tableName);
}

/// 测试结果
class InsertResult {
  final bool success;
  final TestRecord? record;
  final String? error;
  final Duration duration;

  InsertResult({
    required this.success,
    this.record,
    this.error,
    required this.duration,
  });

  @override
  String toString() {
    if (success) {
      return 'InsertResult(success: true, duration: ${duration.inMilliseconds}ms)';
    } else {
      return 'InsertResult(success: false, error: $error, duration: ${duration.inMilliseconds}ms)';
    }
  }
}

/// 批量插入结果
class BatchInsertResult {
  final int totalRecords;
  final int successCount;
  final int failCount;
  final List<TestRecord> successfulRecords;
  final List<String> errors;
  final Duration totalDuration;

  BatchInsertResult({
    required this.totalRecords,
    required this.successCount,
    required this.failCount,
    required this.successfulRecords,
    required this.errors,
    required this.totalDuration,
  });

  double get successRate =>
      totalRecords > 0 ? (successCount / totalRecords) * 100 : 0;
  double get recordsPerSecond => totalDuration.inMilliseconds > 0
      ? successCount / (totalDuration.inMilliseconds / 1000.0)
      : 0;

  @override
  String toString() {
    return 'BatchInsertResult('
        'total: $totalRecords, '
        'success: $successCount, '
        'failed: $failCount, '
        'success_rate: ${successRate.toStringAsFixed(1)}%, '
        'records/s: ${recordsPerSecond.toStringAsFixed(2)}, '
        'duration: ${totalDuration.inMilliseconds}ms)';
  }
}
