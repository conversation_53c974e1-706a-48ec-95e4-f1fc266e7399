class TestRecord {
  final String? id;
  final String name;
  final String email;
  final int age;
  final double score;
  final DateTime? createdAt;
  final String description;

  TestRecord({
    this.id,
    required this.name,
    required this.email,
    required this.age,
    required this.score,
    this.createdAt,
    required this.description,
  });

  factory TestRecord.fromJson(Map<String, dynamic> json) {
    return TestRecord(
      id: json['id']?.toString(), // 支持整数和字符串ID
      name: json['name']?.toString() ?? '', // 支持各种类型转换为字符串
      email: json['email']?.toString() ?? '', // 支持各种类型转换为字符串
      age: json['age'] as int,
      score: (json['score'] as num).toDouble(),
      createdAt: json['created_at'] != null
          ? (json['created_at'] is int
              ? DateTime.fromMillisecondsSinceEpoch(json['created_at'] * 1000)
              : DateTime.parse(json['created_at'] as String))
          : null,
      description: json['description']?.toString() ?? '', // 支持各种类型转换为字符串
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'email': email,
      'age': age,
      'score': score,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      'description': description,
    };
  }

  @override
  String toString() {
    return 'TestRecord{id: $id, name: $name, email: $email, age: $age, score: $score}';
  }
}

class PerformanceResult {
  final String backend;
  final String testType;
  final int totalRecords;
  final int successfulInserts;
  final int failedInserts;
  final double totalTimeSeconds;
  final double averageTimePerRecord;
  final double recordsPerSecond;
  final List<double> batchTimes;
  final List<String> errors;
  final DateTime timestamp;

  PerformanceResult({
    required this.backend,
    required this.testType,
    required this.totalRecords,
    required this.successfulInserts,
    required this.failedInserts,
    required this.totalTimeSeconds,
    required this.averageTimePerRecord,
    required this.recordsPerSecond,
    required this.batchTimes,
    required this.errors,
    required this.timestamp,
  });

  factory PerformanceResult.fromJson(Map<String, dynamic> json) {
    return PerformanceResult(
      backend: json['backend'] as String,
      testType: json['testType'] as String,
      totalRecords: json['totalRecords'] as int,
      successfulInserts: json['successfulInserts'] as int,
      failedInserts: json['failedInserts'] as int,
      totalTimeSeconds: (json['totalTimeSeconds'] as num).toDouble(),
      averageTimePerRecord: (json['averageTimePerRecord'] as num).toDouble(),
      recordsPerSecond: (json['recordsPerSecond'] as num).toDouble(),
      batchTimes: (json['batchTimes'] as List)
          .map((e) => (e as num).toDouble())
          .toList(),
      errors: (json['errors'] as List).map((e) => e as String).toList(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'backend': backend,
      'testType': testType,
      'totalRecords': totalRecords,
      'successfulInserts': successfulInserts,
      'failedInserts': failedInserts,
      'totalTimeSeconds': totalTimeSeconds,
      'averageTimePerRecord': averageTimePerRecord,
      'recordsPerSecond': recordsPerSecond,
      'batchTimes': batchTimes,
      'errors': errors,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  double get successRate {
    if (totalRecords <= 0) return 0;
    // 确保成功率不会超过100%，防止重复计数导致的异常
    final rate = (successfulInserts / totalRecords) * 100;
    return rate > 100 ? 100 : rate;
  }

  @override
  String toString() {
    return '''
Performance Result - $backend ($testType):
  Total Records: $totalRecords
  Successful: $successfulInserts (${successRate.toStringAsFixed(1)}%)
  Failed: $failedInserts
  Total Time: ${totalTimeSeconds.toStringAsFixed(2)}s
  Records/Second: ${recordsPerSecond.toStringAsFixed(2)}
  Avg Time/Record: ${(averageTimePerRecord * 1000).toStringAsFixed(2)}ms
''';
  }
}
