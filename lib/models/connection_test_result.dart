/// 连接测试结果模型
class ConnectionTestResult {
  final String backendName;
  final String connectionType; // 'SSE' or 'WebSocket'
  final int targetConnections;
  final int successfulConnections;
  final int failedConnections;
  final int disconnectedConnections;
  final Duration testDuration;
  final List<int> messageLatencies; // 消息延迟列表(毫秒)
  final double memoryUsageMB;
  final double cpuUsagePercent;
  final DateTime startTime;
  final DateTime endTime;
  final List<String> errors;

  ConnectionTestResult({
    required this.backendName,
    required this.connectionType,
    required this.targetConnections,
    required this.successfulConnections,
    required this.failedConnections,
    required this.disconnectedConnections,
    required this.testDuration,
    required this.messageLatencies,
    required this.memoryUsageMB,
    required this.cpuUsagePercent,
    required this.startTime,
    required this.endTime,
    required this.errors,
  });

  /// 连接成功率
  double get connectionSuccessRate {
    if (targetConnections == 0) return 0.0;
    return (successfulConnections / targetConnections) * 100;
  }

  /// 连接稳定性（不意外断开率）
  double get connectionStabilityRate {
    if (successfulConnections == 0) return 0.0;
    final stableConnections = successfulConnections - disconnectedConnections;
    return (stableConnections / successfulConnections) * 100;
  }

  /// 平均消息延迟
  double get averageMessageLatency {
    if (messageLatencies.isEmpty) return 0.0;
    return messageLatencies.reduce((a, b) => a + b) / messageLatencies.length;
  }

  /// 最大消息延迟
  int get maxMessageLatency {
    if (messageLatencies.isEmpty) return 0;
    return messageLatencies.reduce((a, b) => a > b ? a : b);
  }

  /// 最小消息延迟
  int get minMessageLatency {
    if (messageLatencies.isEmpty) return 0;
    return messageLatencies.reduce((a, b) => a < b ? a : b);
  }

  /// P95延迟
  int get p95MessageLatency {
    if (messageLatencies.isEmpty) return 0;
    final sorted = List<int>.from(messageLatencies)..sort();
    final index = (sorted.length * 0.95).floor();
    return sorted[index.clamp(0, sorted.length - 1)];
  }

  /// 测试是否成功
  bool get isSuccessful {
    return connectionSuccessRate >= 90.0 && 
           connectionStabilityRate >= 95.0 && 
           averageMessageLatency <= 1000.0;
  }

  /// 测试状态描述
  String get statusDescription {
    if (connectionSuccessRate < 90.0) {
      return '❌ 连接成功率过低 (${connectionSuccessRate.toStringAsFixed(1)}%)';
    }
    if (connectionStabilityRate < 95.0) {
      return '⚠️ 连接不稳定 (${connectionStabilityRate.toStringAsFixed(1)}% 稳定)';
    }
    if (averageMessageLatency > 1000.0) {
      return '⚠️ 延迟过高 (${averageMessageLatency.toStringAsFixed(0)}ms)';
    }
    return '✓ 测试通过';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'backendName': backendName,
      'connectionType': connectionType,
      'targetConnections': targetConnections,
      'successfulConnections': successfulConnections,
      'failedConnections': failedConnections,
      'disconnectedConnections': disconnectedConnections,
      'testDurationSeconds': testDuration.inSeconds,
      'connectionSuccessRate': connectionSuccessRate,
      'connectionStabilityRate': connectionStabilityRate,
      'averageMessageLatency': averageMessageLatency,
      'maxMessageLatency': maxMessageLatency,
      'minMessageLatency': minMessageLatency,
      'p95MessageLatency': p95MessageLatency,
      'memoryUsageMB': memoryUsageMB,
      'cpuUsagePercent': cpuUsagePercent,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'isSuccessful': isSuccessful,
      'statusDescription': statusDescription,
      'errors': errors,
    };
  }

  @override
  String toString() {
    return 'ConnectionTestResult('
        'backend: $backendName, '
        'type: $connectionType, '
        'target: $targetConnections, '
        'successful: $successfulConnections, '
        'success_rate: ${connectionSuccessRate.toStringAsFixed(1)}%, '
        'stability: ${connectionStabilityRate.toStringAsFixed(1)}%, '
        'avg_latency: ${averageMessageLatency.toStringAsFixed(0)}ms'
        ')';
  }
}

/// 连接测试会话结果
class ConnectionTestSession {
  final List<ConnectionTestResult> results;
  final DateTime sessionStartTime;
  final DateTime sessionEndTime;

  ConnectionTestSession({
    required this.results,
    required this.sessionStartTime,
    required this.sessionEndTime,
  });

  /// 会话总时长
  Duration get totalDuration => sessionEndTime.difference(sessionStartTime);

  /// 按后端分组的结果
  Map<String, List<ConnectionTestResult>> get resultsByBackend {
    final grouped = <String, List<ConnectionTestResult>>{};
    for (final result in results) {
      grouped.putIfAbsent(result.backendName, () => []).add(result);
    }
    return grouped;
  }

  /// 获取最大稳定连接数
  Map<String, int> get maxStableConnections {
    final maxConnections = <String, int>{};
    for (final entry in resultsByBackend.entries) {
      int maxStable = 0;
      for (final result in entry.value) {
        if (result.isSuccessful) {
          maxStable = result.targetConnections;
        }
      }
      maxConnections[entry.key] = maxStable;
    }
    return maxConnections;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'results': results.map((r) => r.toJson()).toList(),
      'sessionStartTime': sessionStartTime.toIso8601String(),
      'sessionEndTime': sessionEndTime.toIso8601String(),
      'totalDurationSeconds': totalDuration.inSeconds,
      'maxStableConnections': maxStableConnections,
    };
  }
}
