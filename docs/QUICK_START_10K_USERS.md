# 🚀 1万用户写入压力测试快速开始指南

## 📋 概述

本指南将帮助您快速运行1万用户的写入压力测试，评估您的VPS服务器在极限负载下的性能表现。

## ⚡ 快速开始

### 方法1：使用便捷脚本（推荐）

```bash
# Linux/macOS
./scripts/run_10k_users_test.sh

# Windows
scripts\run_10k_users_test.bat
```

### 方法2：直接命令行

```bash
# 运行1万用户写入压力测试
dart run bin/vps_stress_test.dart \
  -c config/10k_users_write_test.json \
  -t write \
  --generate-markdown \
  --generate-json \
  --generate-csv
```

### 方法3：自定义参数

```bash
# 自定义服务器和测试时长
dart run bin/vps_stress_test.dart \
  -c config/10k_users_write_test.json \
  -t write \
  --server-url=http://your-server.com:8090 \
  --test-duration=60
```

## 🎯 测试配置详解

### 默认配置参数

| 参数 | 值 | 说明 |
|------|----|----- |
| 初始用户数 | 10 | 测试开始时的用户数量 |
| 最大用户数 | 10,000 | 目标最大用户数量 |
| 用户递增步长 | 50 | 每次增加的用户数 |
| 递增间隔 | 30秒 | 每次增加用户的时间间隔 |
| 每用户记录数 | 5 | 每个用户写入的记录数量 |
| 成功率阈值 | 90% | 低于此成功率时停止测试 |
| 响应时间阈值 | 2000ms | 超过此时间认为性能下降 |
| 测试持续时间 | 120分钟 | 整个测试的最大持续时间 |

### 测试流程

1. **预热阶段** (10分钟)
   - 从10个用户开始
   - 系统预热，建立连接

2. **压力递增阶段** (90分钟)
   - 每30秒增加50个用户
   - 持续监控性能指标
   - 自动调整负载

3. **极限测试阶段** (20分钟)
   - 维持最大用户数
   - 评估稳定性

4. **报告生成**
   - 生成详细测试报告
   - 提供优化建议

## 📊 预期测试结果

### 不同服务器配置的预期表现

| 服务器配置 | 预期最大用户数 | 平均响应时间 | 成功率 | 测试时长 |
|------------|----------------|--------------|--------|----------|
| 1核1GB | 50-100 | 800-1500ms | 85-95% | 15-30分钟 |
| 2核2GB | 200-500 | 400-800ms | 90-98% | 30-60分钟 |
| 4核4GB | 1000-2000 | 200-400ms | 95-99% | 60-90分钟 |
| 8核8GB | 3000-5000 | 100-200ms | 98-99% | 90-120分钟 |
| 16核16GB | 5000-10000 | 50-100ms | 99%+ | 120分钟+ |

### 关键性能指标

- **最大并发用户数**: 服务器能稳定支持的用户数量
- **平均响应时间**: 请求处理的平均耗时
- **95%响应时间**: 95%请求的响应时间上限
- **成功率**: 成功处理请求的百分比
- **吞吐量**: 每秒处理的请求数量

## 🔍 实时监控

### 测试过程中的输出示例

```
🚀 开始VPS写入压力测试
   服务器: PocketBase
   表名: vps_stress_test

📊 开始实时性能监控
   指标收集间隔: 10秒
   性能检查间隔: 30秒

📈 用户数递增: 10 -> 60 (步长: 50)
✅ 性能良好 - 用户数: 60, 成功率: 98.5%, 平均响应时间: 145ms

📈 用户数递增: 60 -> 110 (步长: 50)
✅ 性能良好 - 用户数: 110, 成功率: 97.2%, 平均响应时间: 189ms

📈 用户数递增: 1950 -> 2000 (步长: 50)
⚠️ 性能下降 - 用户数: 2000, 成功率: 89.1%, 平均响应时间: 2150ms
🛑 达到性能阈值，停止测试

✅ 测试完成！最大并发用户数: 1950
```

## 📈 测试报告

### 生成的报告文件

测试完成后，会在 `reports/` 目录生成以下文件：

1. **Markdown报告** (`vps_write_stress_test_YYYYMMDD_HHMMSS.md`)
   - 可读性强的测试总结
   - 性能分析和建议
   - 图表和趋势分析

2. **JSON数据** (`vps_write_stress_test_YYYYMMDD_HHMMSS.json`)
   - 结构化的测试结果
   - 详细的性能指标
   - 便于程序处理

3. **CSV文件** (`vps_write_stress_test_YYYYMMDD_HHMMSS.csv`)
   - 表格格式的数据
   - 便于Excel分析
   - 支持数据可视化

### 报告内容包括

- 测试配置和环境信息
- 最大并发用户数
- 响应时间分布（P50, P95, P99）
- 错误率分析
- 吞吐量趋势
- 性能优化建议

## 🚨 注意事项

### ⚠️ 测试前准备

1. **确保测试环境**
   - 不要在生产环境运行
   - 使用专门的测试服务器
   - 确保网络连接稳定

2. **服务器准备**
   - 检查服务器资源充足
   - 确保数据库连接正常
   - 备份重要数据

3. **时间安排**
   - 预留2-3小时测试时间
   - 避免业务高峰期
   - 通知相关团队

### 🔧 故障排除

**常见问题及解决方案:**

1. **连接超时**
   ```bash
   # 增加超时时间
   --timeout-threshold=10000
   ```

2. **内存不足**
   ```bash
   # 减少最大用户数
   修改配置文件中的 max_users 参数
   ```

3. **网络延迟高**
   ```bash
   # 调整响应时间阈值
   修改配置文件中的 response_time_threshold_ms
   ```

## 🎯 优化建议

### 根据测试结果优化

1. **如果最大用户数低于预期**
   - 增加服务器CPU和内存
   - 优化数据库连接池
   - 使用缓存减少数据库压力

2. **如果响应时间过高**
   - 优化数据库查询
   - 增加SSD存储
   - 实施负载均衡

3. **如果成功率偏低**
   - 检查错误日志
   - 优化错误处理
   - 增加重试机制

## 📞 获取帮助

- 详细文档: [VPS_STRESS_TEST_GUIDE.md](VPS_STRESS_TEST_GUIDE.md)
- 配置说明: [config/VPS_CONFIG_GUIDE.md](../config/VPS_CONFIG_GUIDE.md)
- 项目概述: [README_VPS_STRESS_TEST.md](../README_VPS_STRESS_TEST.md)

## 🎉 开始测试

现在您已经了解了所有必要信息，可以开始您的1万用户压力测试了！

```bash
# 开始您的压力测试之旅
./scripts/run_10k_users_test.sh
```

祝您测试顺利！🚀
