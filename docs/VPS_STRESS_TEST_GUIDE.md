# VPS服务器极限压力测试指南

## 📋 概述

VPS压力测试工具是一个专门设计用于测试服务器极限性能的工具，能够模拟大量用户同时进行写入、读取和WebSocket连接操作，帮助您找到服务器的真实性能边界。

## 🚀 快速开始

### 1. 基本使用

```bash
# 使用默认配置运行完整测试
dart run bin/vps_stress_test.dart

# 显示帮助信息
dart run bin/vps_stress_test.dart --help
```

### 2. 指定测试类型

```bash
# 只运行写入压力测试
dart run bin/vps_stress_test.dart -t write

# 只运行读取压力测试
dart run bin/vps_stress_test.dart -t read

# 只运行WebSocket压力测试
dart run bin/vps_stress_test.dart -t websocket

# 运行所有测试（默认）
dart run bin/vps_stress_test.dart -t all
```

### 3. 自定义配置

```bash
# 使用自定义配置文件
dart run bin/vps_stress_test.dart -c config/my_config.json

# 覆盖服务器URL
dart run bin/vps_stress_test.dart --server-url=http://your-server.com:8090

# 设置测试持续时间（分钟）
dart run bin/vps_stress_test.dart --test-duration=60
```

## 📊 1万用户写入压力测试示例

### 步骤1：创建配置文件

创建 `config/10k_users_write_test.json`：

```json
{
  "test_settings": {
    "test_duration_minutes": 120,
    "ramp_up_duration_minutes": 10,
    "cool_down_duration_minutes": 5,
    "success_rate_threshold": 0.90,
    "response_time_threshold_ms": 2000.0,
    "timeout_threshold_ms": 5000
  },
  "write_stress_test": {
    "enabled": true,
    "initial_users": 10,
    "max_users": 10000,
    "user_increment_step": 50,
    "increment_interval_seconds": 30,
    "records_per_user": 5,
    "user_action_interval_min_ms": 200,
    "user_action_interval_max_ms": 1000
  },
  "read_stress_test": {
    "enabled": false
  },
  "websocket_stress_test": {
    "enabled": false
  },
  "server_config": {
    "server_type": "pocketbase",
    "server_url": "http://your-server.com:8090",
    "server_name": "Production Server",
    "admin_email": "<EMAIL>",
    "admin_password": "your-password"
  },
  "monitoring": {
    "metrics_collection_interval_seconds": 10,
    "performance_check_interval_seconds": 30,
    "alert_on_high_response_time": true,
    "alert_on_low_success_rate": true
  },
  "reporting": {
    "generate_markdown_report": true,
    "generate_json_report": true,
    "generate_csv_report": true,
    "output_directory": "reports"
  }
}
```

### 步骤2：运行测试

```bash
# 运行1万用户写入压力测试
dart run bin/vps_stress_test.dart \
  -c config/10k_users_write_test.json \
  -t write \
  --generate-markdown \
  --generate-json \
  --generate-csv
```

### 步骤3：监控测试过程

测试运行时，您会看到类似以下的实时输出：

```
🚀 开始VPS写入压力测试
   服务器: PocketBase
   表名: vps_stress_test

📊 开始实时性能监控
   指标收集间隔: 10秒
   性能检查间隔: 30秒

🚀 开始渐进式负载测试
   初始用户数: 10
   最大用户数: 10000
   基础递增步长: 50
   递增间隔: 30秒

📈 用户数递增: 10 -> 60 (步长: 50)
✅ 性能良好 - 用户数: 60, 成功率: 98.5%, 平均响应时间: 145ms

📈 用户数递增: 60 -> 110 (步长: 50)
✅ 性能良好 - 用户数: 110, 成功率: 97.2%, 平均响应时间: 189ms

📈 用户数递增: 110 -> 160 (步长: 50)
⚠️ 性能下降 - 用户数: 160, 成功率: 89.1%, 平均响应时间: 2150ms
🛑 达到性能阈值，停止测试
```

### 步骤4：分析测试结果

测试完成后，会在 `reports` 目录生成以下报告：

- `vps_write_stress_test_YYYYMMDD_HHMMSS.md` - 可读性报告
- `vps_write_stress_test_YYYYMMDD_HHMMSS.json` - 结构化数据
- `vps_write_stress_test_YYYYMMDD_HHMMSS.csv` - 数据分析文件

## 🔧 配置参数详解

### 测试基本设置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `test_duration_minutes` | 测试持续时间（分钟） | 30 |
| `ramp_up_duration_minutes` | 预热时间（分钟） | 5 |
| `success_rate_threshold` | 成功率阈值（0-1） | 0.95 |
| `response_time_threshold_ms` | 响应时间阈值（毫秒） | 5000 |

### 写入压力测试设置

| 参数 | 说明 | 推荐值（1万用户） |
|------|------|------------------|
| `initial_users` | 初始用户数 | 10 |
| `max_users` | 最大用户数 | 10000 |
| `user_increment_step` | 用户递增步长 | 50-100 |
| `increment_interval_seconds` | 递增间隔（秒） | 30-60 |
| `records_per_user` | 每用户记录数 | 1-10 |

## 📈 性能调优建议

### 1. 渐进式增长策略

```bash
# 保守策略：慢速增长，适合生产环境测试
--initial-users=5 --max-users=10000 --increment-step=25 --increment-interval=60

# 激进策略：快速增长，适合压力极限测试
--initial-users=50 --max-users=10000 --increment-step=100 --increment-interval=30
```

### 2. 监控阈值设置

```bash
# 严格阈值：高质量服务要求
--success-rate-threshold=0.99 --response-time-threshold=1000

# 宽松阈值：极限性能测试
--success-rate-threshold=0.85 --response-time-threshold=3000
```

### 3. 服务器优化建议

在运行大规模测试前，确保：

1. **数据库连接池**：设置足够大的连接池
2. **内存配置**：确保有足够内存处理并发请求
3. **网络带宽**：确保网络带宽足够
4. **磁盘I/O**：使用SSD提高写入性能

## 🚨 注意事项

### 1. 测试环境

- ⚠️ **不要在生产环境运行大规模压力测试**
- 🔧 使用专门的测试环境或开发环境
- 📊 确保测试环境配置与生产环境相似

### 2. 资源监控

在测试过程中监控：
- CPU使用率
- 内存使用率
- 磁盘I/O
- 网络带宽
- 数据库连接数

### 3. 优雅停止

```bash
# 使用 Ctrl+C 优雅停止测试
# 系统会自动清理资源并生成报告
```

## 📋 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加超时时间
   --timeout-threshold=10000
   ```

2. **内存不足**
   ```bash
   # 减少并发用户数
   --max-users=5000 --increment-step=25
   ```

3. **网络延迟高**
   ```bash
   # 调整响应时间阈值
   --response-time-threshold=3000
   ```

## 📊 报告解读

### 关键指标

- **最大并发用户数**：服务器能稳定支持的最大用户数
- **平均响应时间**：请求的平均处理时间
- **95%响应时间**：95%请求的响应时间上限
- **成功率**：成功请求的百分比
- **吞吐量**：每秒处理的请求数

### 性能评估

| 成功率 | 响应时间 | 评估 |
|--------|----------|------|
| >99% | <500ms | 优秀 |
| >95% | <1000ms | 良好 |
| >90% | <2000ms | 可接受 |
| <90% | >2000ms | 需要优化 |

## 🎯 实战案例

### 案例1：电商网站写入性能测试

**场景**：双11期间，预计1万用户同时下单

```bash
# 配置文件：config/ecommerce_write_test.json
{
  "write_stress_test": {
    "initial_users": 100,
    "max_users": 10000,
    "user_increment_step": 200,
    "increment_interval_seconds": 45,
    "records_per_user": 3,
    "user_action_interval_min_ms": 500,
    "user_action_interval_max_ms": 2000
  },
  "test_settings": {
    "success_rate_threshold": 0.98,
    "response_time_threshold_ms": 1500.0
  }
}

# 运行测试
dart run bin/vps_stress_test.dart -c config/ecommerce_write_test.json -t write
```

### 案例2：社交媒体读取性能测试

**场景**：热门内容，2万用户同时浏览

```bash
# 配置文件：config/social_read_test.json
{
  "read_stress_test": {
    "initial_users": 200,
    "max_users": 20000,
    "user_increment_step": 300,
    "increment_interval_seconds": 30,
    "queries_per_user": 10,
    "query_types": ["id", "condition", "pagination"]
  }
}

# 运行测试
dart run bin/vps_stress_test.dart -c config/social_read_test.json -t read
```

### 案例3：实时聊天WebSocket测试

**场景**：聊天应用，5000个同时在线用户

```bash
# 配置文件：config/chat_websocket_test.json
{
  "websocket_stress_test": {
    "initial_connections": 50,
    "max_connections": 5000,
    "connection_increment_step": 100,
    "increment_interval_seconds": 60,
    "connection_hold_duration_minutes": 30
  }
}

# 运行测试
dart run bin/vps_stress_test.dart -c config/chat_websocket_test.json -t websocket
```

## 🔍 高级功能

### 1. 混合负载测试

同时测试写入、读取和WebSocket：

```bash
dart run bin/vps_stress_test.dart \
  --write-test \
  --read-test \
  --websocket-test \
  --test-duration=180
```

### 2. 多服务器对比测试

```bash
# 测试PocketBase
dart run bin/vps_stress_test.dart \
  --server-url=http://server1:8090 \
  --server-name="PocketBase Server" \
  -t write

# 测试TrailBase
dart run bin/vps_stress_test.dart \
  --server-url=http://server2:4000 \
  --server-name="TrailBase Server" \
  -t write
```

### 3. 自动化测试脚本

创建 `scripts/auto_stress_test.sh`：

```bash
#!/bin/bash

echo "开始自动化压力测试..."

# 1. 写入性能测试
echo "1/3 写入性能测试..."
dart run bin/vps_stress_test.dart -t write --test-duration=60

# 2. 读取性能测试
echo "2/3 读取性能测试..."
dart run bin/vps_stress_test.dart -t read --test-duration=60

# 3. WebSocket性能测试
echo "3/3 WebSocket性能测试..."
dart run bin/vps_stress_test.dart -t websocket --test-duration=60

echo "所有测试完成！查看reports目录获取详细报告。"
```

## 📈 性能基准参考

### 不同配置服务器的预期性能

| 服务器配置 | 并发写入用户 | 并发读取用户 | WebSocket连接 |
|------------|--------------|--------------|---------------|
| 1核1GB | 50-100 | 200-500 | 100-300 |
| 2核2GB | 200-500 | 1000-2000 | 500-1000 |
| 4核4GB | 1000-2000 | 3000-5000 | 2000-3000 |
| 8核8GB | 3000-5000 | 8000-10000 | 5000-8000 |

### 数据库类型性能对比

| 数据库 | 写入TPS | 读取QPS | 特点 |
|--------|---------|---------|------|
| SQLite | 1000-3000 | 5000-10000 | 轻量级，适合小规模 |
| PostgreSQL | 5000-15000 | 20000-50000 | 功能强大，适合复杂查询 |
| MongoDB | 10000-30000 | 30000-80000 | 文档型，适合灵活数据 |

## 🛠️ 自定义扩展

### 1. 添加自定义测试场景

创建 `lib/tests/custom_stress_test.dart`：

```dart
class CustomStressTest {
  // 实现您的自定义测试逻辑
  Future<void> runCustomTest() async {
    // 自定义测试代码
  }
}
```

### 2. 自定义性能指标

```dart
class CustomMetrics {
  double customResponseTime;
  int customSuccessCount;

  // 添加您需要的指标
}
```

## 📞 技术支持

### 常用命令速查

```bash
# 快速测试
dart run bin/vps_stress_test.dart -t write --test-duration=5

# 详细日志
dart run bin/vps_stress_test.dart -t write --verbose

# 静默模式
dart run bin/vps_stress_test.dart -t write --quiet

# 导出配置模板
dart run bin/vps_stress_test.dart --export-config=my_config.json
```

### 性能调优检查清单

- [ ] 服务器资源充足（CPU、内存、磁盘）
- [ ] 数据库连接池配置合理
- [ ] 网络带宽足够
- [ ] 防火墙和安全组配置正确
- [ ] 监控系统已部署
- [ ] 备份和恢复策略已制定

通过这个详细指南，您可以有效地使用VPS压力测试工具来评估服务器的真实性能极限，并根据测试结果进行针对性的优化。
