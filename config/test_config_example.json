{"insert_tests": {"enable_single_insert": true, "enable_batch_insert": false, "enable_concurrent_insert": false}, "test_parameters": {"total_records": 500, "batch_size": 50, "concurrent_levels": [4], "interval_seconds": 5}, "query_tests": {"enable_id_query": true, "enable_condition_query": true, "enable_range_query": true, "enable_pagination_query": true, "enable_aggregation_query": true, "query_iterations": 1000, "query_batch_size": 100, "query_concurrency_levels": [5, 10, 20]}, "test_stages": {"enable_insert_test": true, "enable_query_test": false, "enable_connection_test": false}, "server_selection": {"pocketbase_only": false, "trailbase_only": false}, "report_generation": {"generate_json_report": false, "generate_csv_report": false, "generate_md_report": true}}