{"test_settings": {"test_duration_minutes": 5, "ramp_up_duration_minutes": 1, "cool_down_duration_minutes": 1, "success_rate_threshold": 0.9, "response_time_threshold_ms": 2000.0, "timeout_threshold_ms": 5000}, "write_stress_test": {"enabled": true, "initial_users": 5, "max_users": 100, "user_increment_step": 10, "increment_interval_seconds": 15, "records_per_user": 3, "user_action_interval_min_ms": 500, "user_action_interval_max_ms": 2000, "batch_size": 1, "enable_batch_insert": false}, "read_stress_test": {"enabled": false, "initial_users": 5, "max_users": 200, "user_increment_step": 10, "increment_interval_seconds": 20, "queries_per_user": 10, "user_action_interval_min_ms": 100, "user_action_interval_max_ms": 500, "query_types": ["id", "condition", "range", "pagination"]}, "websocket_stress_test": {"enabled": false, "initial_connections": 10, "max_connections": 500, "connection_increment_step": 25, "increment_interval_seconds": 15, "connection_hold_duration_minutes": 5, "heartbeat_interval_seconds": 30, "message_frequency_min_ms": 1000, "message_frequency_max_ms": 5000, "connection_timeout_seconds": 30}, "server_config": {"server_type": "pocketbase", "server_url": "http://117.72.60.131:8090", "server_name": "Test PocketBase Server", "admin_email": "<EMAIL>", "admin_password": "lzm_0112333", "test_both_servers": false, "trailbase_url": "http://117.72.60.131:4000"}, "monitoring": {"metrics_collection_interval_seconds": 5, "performance_check_interval_seconds": 15, "alert_on_high_response_time": true, "alert_on_low_success_rate": true, "detailed_logging": false}, "reporting": {"generate_markdown_report": true, "generate_json_report": true, "generate_csv_report": false, "output_directory": "reports", "include_performance_charts": true, "include_recommendations": true}, "initial_data_count": 100, "batch_size": 50, "request_timeout_seconds": 10, "failure_threshold_consecutive": 3}