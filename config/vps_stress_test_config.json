{"test_settings": {"test_duration_minutes": 30, "ramp_up_duration_minutes": 5, "cool_down_duration_minutes": 2, "success_rate_threshold": 0.95, "response_time_threshold_ms": 5000, "timeout_threshold_ms": 10000}, "write_stress_test": {"enabled": true, "initial_users": 1, "max_users": 1000, "user_increment_step": 5, "increment_interval_seconds": 30, "records_per_user": 10, "user_action_interval_ms": [500, 2000], "batch_size": 1, "test_data_size": "medium"}, "read_stress_test": {"enabled": true, "initial_users": 5, "max_users": 2000, "user_increment_step": 10, "increment_interval_seconds": 20, "queries_per_user": 20, "user_action_interval_ms": [200, 1000], "query_types": ["id", "condition", "range", "pagination"], "query_complexity": "mixed"}, "websocket_stress_test": {"enabled": true, "initial_connections": 10, "max_connections": 5000, "connection_increment_step": 50, "increment_interval_seconds": 15, "heartbeat_interval_seconds": 30, "message_frequency_seconds": [5, 15], "connection_hold_duration_minutes": 10, "message_size_bytes": 256}, "monitoring": {"metrics_collection_interval_seconds": 5, "performance_check_interval_seconds": 10, "auto_stop_on_failure": true, "failure_threshold_consecutive": 3, "memory_usage_threshold_mb": 8192, "cpu_usage_threshold_percent": 90}, "server_config": {"pocketbase_url": "http://117.72.60.131:8090", "trailbase_url": "http://117.72.60.131:4000", "test_both_servers": true, "connection_timeout_seconds": 30, "request_timeout_seconds": 15}, "reporting": {"generate_real_time_charts": true, "save_detailed_logs": true, "export_csv_data": true, "generate_performance_curves": true, "include_recommendations": true}}