# VPS压力测试配置文件详细指南

## 📋 配置文件概述

VPS压力测试系统使用JSON格式的配置文件来定义测试参数。本文档详细说明了所有可用的配置选项。

## 📁 配置文件列表

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `vps_stress_test_config.json` | 默认配置 | 标准的压力测试配置 |
| `10k_users_write_test.json` | 1万用户写入测试 | 专门用于大规模写入测试 |
| `test_config.json` | 原有测试配置 | 兼容原有的插入测试配置 |
| `servers.json` | 服务器配置 | PocketBase和TrailBase连接信息 |

## 🔧 配置结构详解

### 1. 测试基本设置 (`test_settings`)

```json
{
  "test_settings": {
    "test_duration_minutes": 30,        // 测试持续时间(分钟)
    "ramp_up_duration_minutes": 5,      // 预热时间(分钟)
    "cool_down_duration_minutes": 2,    // 冷却时间(分钟)
    "success_rate_threshold": 0.95,     // 成功率阈值(0-1)
    "response_time_threshold_ms": 5000.0, // 响应时间阈值(毫秒)
    "timeout_threshold_ms": 10000       // 超时阈值(毫秒)
  }
}
```

**参数说明:**
- `test_duration_minutes`: 整个测试的持续时间
- `ramp_up_duration_minutes`: 测试开始前的预热时间
- `success_rate_threshold`: 低于此成功率时停止测试
- `response_time_threshold_ms`: 超过此响应时间认为性能下降

### 2. 写入压力测试 (`write_stress_test`)

```json
{
  "write_stress_test": {
    "enabled": true,                    // 是否启用写入测试
    "initial_users": 1,                 // 初始用户数
    "max_users": 1000,                  // 最大用户数
    "user_increment_step": 5,           // 用户递增步长
    "increment_interval_seconds": 30,   // 递增间隔(秒)
    "records_per_user": 10,             // 每用户记录数
    "user_action_interval_min_ms": 500, // 用户操作最小间隔
    "user_action_interval_max_ms": 2000, // 用户操作最大间隔
    "batch_size": 1,                    // 批量大小
    "enable_batch_insert": false        // 是否启用批量插入
  }
}
```

### 3. 读取压力测试 (`read_stress_test`)

```json
{
  "read_stress_test": {
    "enabled": true,                    // 是否启用读取测试
    "initial_users": 5,                 // 初始用户数
    "max_users": 2000,                  // 最大用户数
    "user_increment_step": 10,          // 用户递增步长
    "increment_interval_seconds": 20,   // 递增间隔(秒)
    "queries_per_user": 20,             // 每用户查询数
    "user_action_interval_min_ms": 100, // 用户操作最小间隔
    "user_action_interval_max_ms": 500, // 用户操作最大间隔
    "query_types": ["id", "condition", "range", "pagination"] // 查询类型
  }
}
```

### 4. WebSocket压力测试 (`websocket_stress_test`)

```json
{
  "websocket_stress_test": {
    "enabled": true,                    // 是否启用WebSocket测试
    "initial_connections": 10,          // 初始连接数
    "max_connections": 5000,            // 最大连接数
    "connection_increment_step": 50,    // 连接递增步长
    "increment_interval_seconds": 15,   // 递增间隔(秒)
    "connection_hold_duration_minutes": 10, // 连接保持时间(分钟)
    "heartbeat_interval_seconds": 30,   // 心跳间隔(秒)
    "message_frequency_min_ms": 1000,   // 消息发送最小频率
    "message_frequency_max_ms": 5000,   // 消息发送最大频率
    "connection_timeout_seconds": 30    // 连接超时时间(秒)
  }
}
```

### 5. 服务器配置 (`server_config`)

```json
{
  "server_config": {
    "server_type": "pocketbase",        // 服务器类型
    "server_url": "http://localhost:8090", // 服务器URL
    "server_name": "Test Server",       // 服务器名称
    "admin_email": "<EMAIL>", // 管理员邮箱
    "admin_password": "password",       // 管理员密码
    "test_both_servers": false,         // 是否测试两个服务器
    "trailbase_url": "http://localhost:4000" // TrailBase服务器URL
  }
}
```

### 6. 监控配置 (`monitoring`)

```json
{
  "monitoring": {
    "metrics_collection_interval_seconds": 5,  // 指标收集间隔
    "performance_check_interval_seconds": 10,  // 性能检查间隔
    "alert_on_high_response_time": true,       // 高响应时间警告
    "alert_on_low_success_rate": true,         // 低成功率警告
    "detailed_logging": true                   // 详细日志
  }
}
```

### 7. 报告配置 (`reporting`)

```json
{
  "reporting": {
    "generate_markdown_report": true,   // 生成Markdown报告
    "generate_json_report": true,       // 生成JSON报告
    "generate_csv_report": true,        // 生成CSV报告
    "output_directory": "reports",      // 输出目录
    "include_performance_charts": true, // 包含性能图表
    "include_recommendations": true     // 包含优化建议
  }
}
```

## 🎯 配置模板

### 小规模测试配置

```json
{
  "test_settings": {
    "test_duration_minutes": 10,
    "success_rate_threshold": 0.95,
    "response_time_threshold_ms": 1000.0
  },
  "write_stress_test": {
    "initial_users": 1,
    "max_users": 100,
    "user_increment_step": 5,
    "increment_interval_seconds": 30
  }
}
```

### 1万用户大规模测试配置

```json
{
  "test_settings": {
    "test_duration_minutes": 120,
    "success_rate_threshold": 0.90,
    "response_time_threshold_ms": 2000.0
  },
  "write_stress_test": {
    "initial_users": 10,
    "max_users": 10000,
    "user_increment_step": 50,
    "increment_interval_seconds": 30,
    "records_per_user": 5
  }
}
```

## 🔍 配置优化建议

### 1. 根据服务器配置调整

| 服务器配置 | 建议最大用户数 | 建议递增步长 | 建议间隔时间 |
|------------|----------------|--------------|--------------|
| 1核1GB | 100 | 5 | 60秒 |
| 2核2GB | 500 | 10 | 45秒 |
| 4核4GB | 2000 | 25 | 30秒 |
| 8核8GB | 5000 | 50 | 30秒 |

### 2. 根据测试目标调整

**性能极限测试:**
- 较低的成功率阈值 (0.80-0.85)
- 较高的响应时间阈值 (3000-5000ms)
- 较大的递增步长

**稳定性测试:**
- 较高的成功率阈值 (0.95-0.99)
- 较低的响应时间阈值 (500-1000ms)
- 较小的递增步长

## 🚀 快速配置生成

### 创建自定义配置

1. 复制现有配置文件：
```bash
cp config/vps_stress_test_config.json config/my_test.json
```

2. 修改关键参数：
```json
{
  "write_stress_test": {
    "max_users": 5000,              // 调整最大用户数
    "user_increment_step": 25,      // 调整递增步长
    "increment_interval_seconds": 45 // 调整间隔时间
  },
  "server_config": {
    "server_url": "http://your-server.com:8090" // 修改服务器地址
  }
}
```

3. 运行测试：
```bash
dart run bin/vps_stress_test.dart -c config/my_test.json
```

## 📝 配置验证

系统会自动验证配置文件的有效性，常见错误包括：

1. **数值范围错误**: 成功率阈值应在0-1之间
2. **时间设置不合理**: 超时时间应大于响应时间阈值
3. **用户数设置错误**: 最大用户数应大于初始用户数
4. **服务器配置缺失**: 必须提供有效的服务器URL

通过合理配置这些参数，您可以精确控制压力测试的行为，获得准确的服务器性能评估结果。
